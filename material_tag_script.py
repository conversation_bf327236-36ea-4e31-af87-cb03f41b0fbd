from http.client import HTTPException
import os
import tos
import aiohttp # Not directly used in the provided snippet for main logic, but kept if other parts rely on it
import asyncio
from concurrent.futures import ThreadPoolExecutor
import logging
import json # For material.json
from PIL import Image # For image processing
import zipfile
import hashlib
# import httpx # Not directly used in main logic based on requirements, kept if other parts rely
import shutil # Not directly used in main logic based on requirements, kept if other parts rely
from pathlib import Path # Not directly used in main logic based on requirements, kept if other parts rely


# 创建logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s') # Basic logging config

# TOS配置
access_key = "AKLTNWIxMzU2ZjkyNWYwNGIyMGIxZWEwMGUxMDRkNzU4NDQ"
access_secret = "T1RneFpUZGxNamt3WWpVd05EZzBObUV5TnpneFpHUXlNVFF4WkRjek1EUQ=="
endpoint = "tos-cn-shanghai.volces.com"
region = "cn-shanghai"
bucket_name = "iclick-material"

# 创建线程池用于执行同步TOS操作
executor = ThreadPoolExecutor(max_workers=10) # Adjust max_workers as needed

async def download_file_async(file_name: str, save_path: str) -> str:
    """异步从TOS下载文件"""
    try:
        # 确保目标目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        loop = asyncio.get_event_loop()
        
        # 在线程池中执行同步TOS操作
        result = await loop.run_in_executor(
            executor,
            _download_from_tos,
            file_name,
            save_path
        )
        
        if not result:
            # _download_from_tos logs its own errors, raising HTTPException here might be redundant if handled by caller
            logger.error(f"下载TOS文件失败: {file_name} -> {save_path}")
            return None # Return None instead of raising HTTPException directly, allows for more control in caller
        
        return save_path
    except tos.exceptions.TosServerError as e:
        logger.error(f"TOS服务端错误 (download_file_async): {e.message}, RequestID: {e.request_id}")
        # Consider re-raising or returning specific error code/None
        return None
    except tos.exceptions.TosClientError as e:
        logger.error(f"TOS客户端错误 (download_file_async): {e.message}")
        return None
    except Exception as e:
        logger.error(f"下载失败 (download_file_async for {file_name}): {str(e)}")
        return None


# 同步TOS下载操作，将在线程池中执行
def _download_from_tos(file_name, local_path):
    try:
        client = tos.TosClientV2(access_key, access_secret, endpoint, region)
        client.get_object_to_file(bucket_name, file_name, local_path)
        logger.info(f"TOS下载成功: {file_name} -> {local_path}")
        return local_path
    except tos.exceptions.TosClientError as e:
        logger.error(f'下载客户端错误 (_download_from_tos for {file_name}): {e.message}, 原因: {e.cause}')
        return None
    except tos.exceptions.TosServerError as e:
        logger.error(f'下载服务端错误 (_download_from_tos for {file_name}): {e.code}, RequestID: {e.request_id}, 消息: {e.message}')
        return None
    except Exception as e:
        logger.error(f'下载未知错误 (_download_from_tos for {file_name}): {e}')
        return None
    

# 同步列举TOS文件，将在线程池中执行
def _list_files_from_tos(prefix="", delimiter="", max_keys=1000, continuation_token=None):
    try:
        client = tos.TosClientV2(access_key, access_secret, endpoint, region)
        params = {
            "bucket": bucket_name,
            "prefix": prefix,
            "delimiter": delimiter,
            "max_keys": max_keys
        }
        if continuation_token:
            params["continuation_token"] = continuation_token
            
        result = client.list_objects_type2(**params)
        
        files_list = []
        if result.contents: # Check if contents is not None
            for item in result.contents:
                files_list.append(item.key)
            
        has_more = result.is_truncated
        next_token = result.next_continuation_token if has_more else None
            
        return {
            "files": files_list,
            "has_more": has_more,
            "next_token": next_token
        }
    except tos.exceptions.TosClientError as e:
        logger.error(f'列举文件客户端错误: {e.message}, 原因: {e.cause}')
        return None
    except tos.exceptions.TosServerError as e:
        logger.error(f'列举文件服务端错误: {e.code}, RequestID: {e.request_id}, 消息: {e.message}')
        return None
    except Exception as e:
        logger.error(f'列举文件未知错误: {e}')
        return None

# 异步列举TOS文件 (single batch)
async def list_files_from_tos_async(prefix="", delimiter="", max_keys=1000):
    loop = asyncio.get_running_loop()
    result = await loop.run_in_executor(
        executor, 
        lambda: _list_files_from_tos(prefix, delimiter, max_keys)
    )
    return result

# 分页列举所有文件的方法（处理大量文件情况）
async def list_all_files_from_tos(prefix="", delimiter="", batch_size=1000):
    all_files = []
    next_token = None
    has_more = True
    
    while has_more:
        loop = asyncio.get_running_loop()
        # Use a lambda to pass the current next_token to the executor
        result = await loop.run_in_executor(
            executor,
            _list_files_from_tos, # Pass function reference
            prefix, delimiter, batch_size, next_token # Pass arguments for the function
        )
                
        if not result or result.get("files") is None : # Check if result is None or files key is missing/None
            logger.error(f'批量列举文件错误或未返回有效结果。停止分页。')
            break
                    
        all_files.extend(result["files"])
        
        has_more = result["has_more"]
        next_token = result["next_token"] if has_more else None
        
        if has_more:
            logger.info(f"列举到 {len(all_files)} 个文件，继续获取下一批...")
    
    return all_files


def get_material_source_download_folder():
    """Get the download folder path, similar to iOS implementation"""
    base_path = os.path.join(os.getcwd(), "all_materials")
    return base_path

async def download_zip_source(url: str, source: str, file_name: str):
    """
    从TOS下载zip文件并解密（与iOS downloadZipSourceFrom等效的Python实现）
    
    参数:
        url: 下载URL（实际上不会使用，因为我们从TOS下载）
        source: 源文件名 (TOS object key, e.g., "creative/123_encrypt.zip")
        file_name: 基础文件名 (e.g., "123_encrypt.zip", used for key generation and temp naming)
        
    返回:
        tuple: (final_path, final_name) 或失败时返回 (None, None)
    """
    folder_path = get_material_source_download_folder()
    # source_basename is the actual file name, e.g., "123_encrypt.zip"
    source_basename = os.path.basename(source) # Use basename of TOS key
    zip_path = os.path.join(folder_path, source_basename) # Temp path for downloaded zip
    
    os.makedirs(folder_path, exist_ok=True)
    
    if "_encrypt" not in source_basename: # Check basename for "_encrypt"
        logger.info(f"非加密文件 {source_basename}，跳过处理")
        return None, None
    
    # MD5哈希密码基于文件名 (e.g., "123_encrypt.zip")
    zip_key = hashlib.md5(source_basename.encode()).hexdigest()
    
    try:
        # 从TOS下载文件 (source is the full TOS object key)
        download_result_path = await download_file_async(source, zip_path)
        
        if not download_result_path or not os.path.exists(zip_path):
            logger.error(f"从TOS下载文件失败: {source}")
            if os.path.exists(zip_path): # Clean up if zip was partially downloaded
                 os.remove(zip_path)
            return None, None
                    
        extracted_files_paths = []
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list_in_zip = zip_ref.namelist()
            if not file_list_in_zip:
                logger.warning(f"Zip文件 {source_basename} 为空。")
                os.remove(zip_path)
                return None, None
            zip_ref.extractall(path=folder_path, pwd=bytes(zip_key, 'utf-8'))
            extracted_files_paths = [os.path.join(folder_path, f) for f in file_list_in_zip]
        
        if os.path.exists(zip_path):
            os.remove(zip_path) # Delete zip after extraction

        if not extracted_files_paths:
            logger.warning(f"解压后没有文件从 {source_basename}")
            return None, None
            
        # Assume first file is the primary content
        extracted_path_original = extracted_files_paths[0]
        extracted_ext = os.path.splitext(extracted_path_original)[1]
        
        # Create new name based on original zip name but with extracted file's extension
        # e.g., if source_basename was "abc_encrypt.zip" and extracted "image.png" -> "abc_encrypt.png"
        new_final_name = os.path.splitext(source_basename)[0] + extracted_ext
        
        if extracted_ext.upper() == '.MP4': # Case correction for MP4
            new_final_name = os.path.splitext(new_final_name)[0] + extracted_ext.lower()
        
        new_final_path = os.path.join(folder_path, new_final_name)
        
        # Rename the extracted file to the new standardized name
        # Ensure no overwrite if new_final_path is different from extracted_path_original
        if extracted_path_original != new_final_path:
            if os.path.exists(new_final_path): # Safety check
                logger.warning(f"目标文件 {new_final_path} 已存在，将被覆盖。")
                os.remove(new_final_path)
            os.rename(extracted_path_original, new_final_path)
        
        # If there were other extracted files, they remain with their original names from the zip
        # This logic assumes one primary file output matching the modified zip name.
        # If multiple files are important, this part might need adjustment.
        if len(extracted_files_paths) > 1:
            logger.info(f"Zip {source_basename} 包含多个文件。主文件重命名为 {new_final_name}。其他文件保留原名: {extracted_files_paths[1:]}")


        return new_final_path, new_final_name # Return path and name of the primary (renamed) extracted file
            
    except zipfile.BadZipFile:
        logger.error(f"文件 {source_basename} 不是有效的zip文件或密码错误。")
        if os.path.exists(zip_path): os.remove(zip_path)
        return None, None
    except RuntimeError as e: # Catches incorrect password for zipfile
        if "Bad password" in str(e):
             logger.error(f"解压文件 {source_basename} 密码错误。")
        else:
             logger.error(f"解压文件 {source_basename} 时发生运行时错误: {e}")
        if os.path.exists(zip_path): os.remove(zip_path)
        return None, None
    except Exception as e:
        logger.error(f"下载或解压文件 {source_basename} 错误: {e}")
        if os.path.exists(zip_path): os.remove(zip_path) # Ensure cleanup
        # Clean up any potentially extracted files if error happened after extraction start
        if 'extracted_files_paths' in locals():
            for fp_to_clean in extracted_files_paths:
                if os.path.exists(fp_to_clean):
                    try:
                        os.remove(fp_to_clean)
                    except OSError:
                        logger.warning(f"无法清理部分解压的文件: {fp_to_clean}")
        return None, None

async def main():        
    # 1. Load material.json
    material_json_path = "material.json" # Make sure this file is in the same directory or provide full path
    try:
        with open(material_json_path, 'r', encoding='utf-8') as f:
            materials_json_obj = json.load(f)
            # Assuming the actual list of materials is under the "material_tags" key
            materials_data_list = materials_json_obj.get("material_tags", [])
            if not materials_data_list:
                logger.error(f"'{material_json_path}' 中未找到 'material_tags' 或列表为空。")
                return
    except FileNotFoundError:
        logger.error(f"'{material_json_path}' 文件未找到。")
        return
    except json.JSONDecodeError:
        logger.error(f"解析 '{material_json_path}' 文件时出错。")
        return

    # Create a dictionary for easy lookup and update using the 'name' field from JSON
    # e.g., "161121996795618_encrypt.zip"
    material_map = {item['name']: item for item in materials_data_list}
    logger.info(f"从 '{material_json_path}' 加载了 {len(material_map)} 条素材信息。")

    # List all files from TOS under the "creative/" prefix
    # This returns full paths like "creative/filename.zip"
    all_tos_files_full_paths = await list_all_files_from_tos(prefix="creative/")
    if not all_tos_files_full_paths:
        logger.info("在TOS的 'creative/' 前缀下未找到文件，或列举文件时出错。")
        # return # Continue to save JSON even if no files found in TOS, in case JSON had changes unrelated to TOS processing
    else:
        logger.info(f"在TOS中找到 {len(all_tos_files_full_paths)} 个文件(在 'creative/' 前缀下)。")

    processed_count = 0
    skipped_not_in_json = 0
    skipped_not_encrypt_zip = 0
    failed_download_extract = 0

    for tos_file_full_key in all_tos_files_full_paths:
        # tos_file_name will be like "161121996795618_encrypt.zip"
        tos_file_name_basename = os.path.basename(tos_file_full_key)

        # Requirement 1: Check if file basename exists in material_map keys
        if tos_file_name_basename not in material_map:
            # logger.debug(f"跳过 '{tos_file_name_basename}': 在 material.json 中未找到。") #  Use debug for less verbose logs
            skipped_not_in_json += 1
            continue
        
        # Further check: ensure it's an _encrypt.zip file we intend to process
        if not tos_file_name_basename.lower().endswith("_encrypt.zip"):
            logger.info(f"跳过 '{tos_file_name_basename}': 文件名不以 '_encrypt.zip' 结尾。")
            skipped_not_encrypt_zip +=1
            continue

        material_entry = material_map[tos_file_name_basename]
        logger.info(f"开始处理 '{tos_file_name_basename}'...")

        # Download and decrypt/extract the file
        # `source` should be the full TOS key: "creative/somefile_encrypt.zip"
        # `file_name` parameter in download_zip_source is used for key generation and temp naming,
        # so the basename is appropriate: "somefile_encrypt.zip"
        extracted_file_path, extracted_final_name = await download_zip_source(
            url=None, 
            source=tos_file_full_key, 
            file_name=tos_file_name_basename 
        )

        if extracted_file_path and os.path.exists(extracted_file_path):
            logger.info(f"成功下载并解压 '{tos_file_name_basename}' -> '{extracted_final_name}' 位于 '{extracted_file_path}'.")
            
            # Requirement 2: Check image data for PNG and alpha channel
            try:
                with Image.open(extracted_file_path) as img: # Use 'with' to ensure file is closed
                    is_png_with_alpha = False
                    if img.format == 'PNG':
                        # Check for alpha in mode. Common modes with alpha: 'RGBA', 'LA'.
                        # 'P' (palette) mode can also have transparency, often indicated by 'transparency' in img.info.
                        if 'A' in img.mode or 'a' in img.mode or (img.mode == 'P' and 'transparency' in img.info):
                            is_png_with_alpha = True
                            
                    if is_png_with_alpha:
                        if material_entry.get('background', True) is True: # Only update if it's currently True
                           material_entry['background'] = False
                           logger.info(f"更新 '{tos_file_name_basename}' 的 'background' 为 False (PNG带Alpha通道)。")
                        else:
                           logger.info(f"'{tos_file_name_basename}' 的 'background' 已为 False (PNG带Alpha通道)。")
                    else:
                        logger.info(f"文件 '{extracted_final_name}' (来自 '{tos_file_name_basename}') 不是带Alpha通道的PNG。格式: {img.format}, 模式: {img.mode}.")
            
            except FileNotFoundError:
                 logger.error(f"解压后的文件 '{extracted_file_path}' 未找到，无法进行图像处理。")
            except Exception as e:
                logger.error(f"处理图像 '{extracted_final_name}' (来自 '{tos_file_name_basename}') 时出错: {e}")
            
            # Requirement 3: Delete the extracted file
            try:
                os.remove(extracted_file_path)
                logger.info(f"已删除解压后的文件: '{extracted_file_path}'.")
            except OSError as e:
                logger.error(f"删除解压后的文件 '{extracted_file_path}' 时出错: {e}")
            
            processed_count += 1
        else:
            logger.error(f"下载或解压 '{tos_file_name_basename}' 失败。")
            failed_download_extract += 1
            # The download_zip_source function should handle deletion of the .zip file on its failure.

    logger.info(f"--- 处理完成 ---")
    logger.info(f"成功处理并检查的文件数: {processed_count}")
    logger.info(f"因未在 material.json 中找到而跳过的文件数: {skipped_not_in_json}")
    logger.info(f"因非 '_encrypt.zip' 文件而跳过的文件数: {skipped_not_encrypt_zip}")
    logger.info(f"下载或解压失败的文件数: {failed_download_extract}")


    # Save the updated material_data back to material.json
    # Reconstruct the original JSON structure with "material_tags" key
    output_json_structure = {"material_tags": list(material_map.values())}
    try:
        with open(material_json_path, 'w', encoding='utf-8') as f:
            json.dump(output_json_structure, f, indent=4, ensure_ascii=False)
        logger.info(f"成功将更新后的数据写入 '{material_json_path}'。")
    except Exception as e:
        logger.error(f"将更新后的数据写入 '{material_json_path}' 时出错: {e}")

if __name__ == "__main__":
    # Setup basic logging
    # logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    # Ensure the folder for downloads exists
    os.makedirs(get_material_source_download_folder(), exist_ok=True)
    
    asyncio.run(main())