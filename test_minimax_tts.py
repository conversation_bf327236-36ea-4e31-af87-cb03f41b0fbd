#!/usr/bin/env python3
"""
MiniMax TTS API测试脚本
用于测试语音合成、音色管理和快速复刻功能
"""

import requests
import json
import time
import os
from typing import Dict, Any

class MinimaxTTSAPITester:
    """MiniMax TTS API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5005"):
        """
        初始化测试器
        
        Args:
            base_url: API服务器地址
        """
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_get_voices(self) -> Dict[str, Any]:
        """测试获取系统音色接口"""
        print("🎵 测试获取系统音色...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/minimax/voices")
            result = response.json()
            
            if result.get('success'):
                voices = result['data']['all_voices']
                categorized = result['data']['categorized_voices']
                
                print(f"✅ 成功获取 {result['data']['total_count']} 个音色")
                print(f"   - 男性音色: {len(categorized['male'])} 个")
                print(f"   - 女性音色: {len(categorized['female'])} 个")
                print(f"   - 儿童音色: {len(categorized['children'])} 个")
                print(f"   - 特色音色: {len(categorized['special'])} 个")
                print(f"   - 英文音色: {len(categorized['english'])} 个")
                
                # 显示部分音色示例
                print("\n📋 部分音色示例:")
                for category, voices_dict in categorized.items():
                    if voices_dict:
                        print(f"   {category}:")
                        for voice_id, voice_name in list(voices_dict.items())[:3]:
                            print(f"     - {voice_id}: {voice_name}")
                
                return result
            else:
                print(f"❌ 获取音色失败: {result.get('error')}")
                return result
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_sync_tts(self, text: str = "你好，这是MiniMax语音合成测试。") -> Dict[str, Any]:
        """测试同步语音合成接口"""
        print("🎤 测试同步语音合成...")
        
        payload = {
            "text": text,
            "model": "speech-02-hd",
            "voice_id": "female-tianmei",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0,
            "emotion": "happy",
            "sample_rate": 32000,
            "format": "mp3",
            "stream": False,
            "output_format": "hex"
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/minimax/tts/sync",
                json=payload,
                timeout=120
            )
            result = response.json()
            
            if result.get('success'):
                data = result['data']
                if 'data' in data and 'audio' in data['data']:
                    audio_length = data.get('extra_info', {}).get('audio_length', 0)
                    audio_size = data.get('extra_info', {}).get('audio_size', 0)
                    print(f"✅ 同步语音合成成功")
                    print(f"   - 音频时长: {audio_length}ms")
                    print(f"   - 音频大小: {audio_size} bytes")
                    print(f"   - 文本: {text[:50]}...")
                else:
                    print("✅ 同步语音合成请求成功，但未获取到音频数据")
                
                return result
            else:
                print(f"❌ 同步语音合成失败: {result.get('error')}")
                return result
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_async_tts(self, text: str = "这是一个异步长文本语音合成测试。" * 10) -> Dict[str, Any]:
        """测试异步语音合成接口"""
        print("⏳ 测试异步语音合成...")
        
        payload = {
            "text": text,
            "model": "speech-02-hd",
            "voice_id": "male-qn-qingse",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0,
            "sample_rate": 32000,
            "format": "mp3",
            "channel": 2
        }
        
        try:
            # 提交异步任务
            response = self.session.post(
                f"{self.base_url}/api/minimax/tts/async",
                json=payload,
                timeout=60
            )
            result = response.json()
            
            if result.get('success'):
                task_data = result['data']
                task_id = task_data.get('task_id')
                
                print(f"✅ 异步任务提交成功")
                print(f"   - 任务ID: {task_id}")
                print(f"   - 文本长度: {len(text)} 字符")
                
                # 查询任务状态
                if task_id:
                    print("🔍 查询任务状态...")
                    query_result = self.test_query_async_task(task_id)
                    return {"submit_result": result, "query_result": query_result}
                
                return result
            else:
                print(f"❌ 异步语音合成失败: {result.get('error')}")
                return result
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_query_async_task(self, task_id: str) -> Dict[str, Any]:
        """测试查询异步任务状态"""
        print(f"🔍 查询异步任务状态: {task_id}")
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/minimax/tts/async/query/{task_id}",
                timeout=30
            )
            result = response.json()
            
            if result.get('success'):
                data = result['data']
                print(f"✅ 任务状态查询成功")
                print(f"   - 任务ID: {task_id}")
                print(f"   - 状态信息: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                return result
            else:
                print(f"❌ 任务状态查询失败: {result.get('error')}")
                return result
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_upload_file(self, file_path: str) -> Dict[str, Any]:
        """测试文件上传接口"""
        print(f"📁 测试文件上传: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return {"success": False, "error": "文件不存在"}
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {'purpose': 'voice_clone'}
                
                response = self.session.post(
                    f"{self.base_url}/api/minimax/upload",
                    files=files,
                    data=data,
                    timeout=120
                )
                result = response.json()
            
            if result.get('success'):
                file_info = result['data']
                print(f"✅ 文件上传成功")
                print(f"   - 文件ID: {file_info.get('file', {}).get('file_id')}")
                print(f"   - 文件名: {os.path.basename(file_path)}")
                
                return result
            else:
                print(f"❌ 文件上传失败: {result.get('error')}")
                return result
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_voice_clone_with_upload(self, file_path: str, voice_id: str = "TestVoice001") -> Dict[str, Any]:
        """测试通过上传进行音色复刻"""
        print(f"🎭 测试音色复刻: {file_path} -> {voice_id}")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return {"success": False, "error": "文件不存在"}
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {
                    'voice_id': voice_id,
                    'demo_text': '这是音色复刻的试听测试文本。',
                    'demo_model': 'speech-02-hd',
                    'accuracy': '0.7',
                    'need_noise_reduction': 'false',
                    'need_volume_normalization': 'false'
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/minimax/voice/clone/upload",
                    files=files,
                    data=data,
                    timeout=180
                )
                result = response.json()
            
            if result.get('success'):
                clone_data = result['data']
                print(f"✅ 音色复刻成功")
                print(f"   - 音色ID: {voice_id}")
                print(f"   - 原始文件: {clone_data.get('original_filename')}")
                
                # 显示复刻结果
                clone_result = clone_data.get('clone_result', {})
                if 'demo_audio' in clone_result:
                    print(f"   - 试听音频: 已生成")
                
                return result
            else:
                print(f"❌ 音色复刻失败: {result.get('error')}")
                return result
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def test_sync_tts_with_tos(self, text: str = "这是TOS集成的语音合成测试。") -> Dict[str, Any]:
        """测试同步语音合成并上传到TOS"""
        print("☁️ 测试同步语音合成并上传到TOS...")

        payload = {
            "text": text,
            "model": "speech-02-hd",
            "voice_id": "female-tianmei",
            "speed": 1.0,
            "vol": 1.0,
            "pitch": 0,
            "emotion": "happy",
            "sample_rate": 32000,
            "format": "mp3"
        }

        try:
            response = self.session.post(
                f"{self.base_url}/api/minimax/tts/sync/tos",
                json=payload,
                timeout=120
            )
            result = response.json()

            if result.get('success'):
                data = result['data']
                tos_file_name = data.get('tos_file_name')
                print(f"✅ 同步语音合成并上传TOS成功")
                print(f"   - TOS文件名: {tos_file_name}")
                print(f"   - 文本: {text[:50]}...")

                return result
            else:
                print(f"❌ 同步语音合成并上传TOS失败: {result.get('error')}")
                return result

        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def test_voice_clone_from_tos(self, tos_file_name: str, voice_id: str = "TestVoiceFromTOS001") -> Dict[str, Any]:
        """测试从TOS进行音色复刻"""
        print(f"🎭 测试从TOS进行音色复刻: {tos_file_name} -> {voice_id}")

        payload = {
            "tos_file_name": tos_file_name,
            "voice_id": voice_id,
            "demo_text": "这是从TOS音色复刻的试听测试文本。",
            "demo_model": "speech-02-hd",
            "accuracy": 0.7,
            "need_noise_reduction": False,
            "need_volume_normalization": False
        }

        try:
            response = self.session.post(
                f"{self.base_url}/api/minimax/voice/clone/tos",
                json=payload,
                timeout=180
            )
            result = response.json()

            if result.get('success'):
                data = result['data']
                print(f"✅ 从TOS音色复刻成功")
                print(f"   - TOS文件名: {data.get('tos_file_name')}")
                print(f"   - 音色ID: {data.get('voice_id')}")

                # 显示复刻结果
                clone_result = data.get('clone_result', {})
                if 'demo_audio' in clone_result:
                    print(f"   - 试听音频: 已生成")

                return result
            else:
                print(f"❌ 从TOS音色复刻失败: {result.get('error')}")
                return result

        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def run_all_tests(self, audio_file_path: str = None, tos_audio_file: str = None):
        """运行所有测试"""
        print("🚀 开始MiniMax TTS API测试")
        print("=" * 50)
        
        # 测试1: 获取音色列表
        self.test_get_voices()
        print()
        
        # 测试2: 同步语音合成
        self.test_sync_tts()
        print()
        
        # 测试3: 异步语音合成
        self.test_async_tts()
        print()

        # 测试4: 同步语音合成并上传到TOS
        tts_tos_result = self.test_sync_tts_with_tos()
        print()

        # 测试5: 文件上传和音色复刻（如果提供了音频文件）
        if audio_file_path and os.path.exists(audio_file_path):
            self.test_upload_file(audio_file_path)
            print()

            self.test_voice_clone_with_upload(audio_file_path)
            print()
        else:
            print("⚠️  未提供音频文件，跳过文件上传和音色复刻测试")
            print()

        # 测试6: 从TOS进行音色复刻（如果提供了TOS音频文件名）
        if tos_audio_file:
            self.test_voice_clone_from_tos(tos_audio_file)
            print()
        else:
            print("⚠️  未提供TOS音频文件名，跳过从TOS音色复刻测试")
            print()

        print("✅ 所有测试完成")

if __name__ == "__main__":
    # 创建测试器
    tester = MinimaxTTSAPITester()

    # 运行测试
    # 如果有音频文件，可以传入路径进行完整测试
    # 如果有TOS中的音频文件，可以传入TOS文件名进行从TOS复刻测试
    # tester.run_all_tests("/path/to/your/audio/file.mp3", "your_tos_audio_file.mp3")
    tester.run_all_tests()
