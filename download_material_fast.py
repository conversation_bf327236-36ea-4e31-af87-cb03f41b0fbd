from http.client import HTTPException
import os
import tos
import aiohttp
import asyncio
from concurrent.futures import ThreadPoolExecutor
import logging

# 创建logger
logger = logging.getLogger(__name__)

# TOS配置
access_key = "AKLTNWIxMzU2ZjkyNWYwNGIyMGIxZWEwMGUxMDRkNzU4NDQ"
access_secret = "T1RneFpUZGxNamt3WWpVd05EZzBObUV5TnpneFpHUXlNVFF4WkRjek1EUQ=="
endpoint = "tos-cn-shanghai.volces.com"
region = "cn-shanghai"
bucket_name = "iclick-material"

# 创建线程池用于执行同步TOS操作
executor = ThreadPoolExecutor()

async def download_file_async(file_name: str, save_path: str) -> str:
    """异步从TOS下载文件"""
    try:
        # 确保目标目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        loop = asyncio.get_event_loop()
        
        # 在线程池中执行同步TOS操作
        result = await loop.run_in_executor(
            executor,
            _download_from_tos,
            file_name,
            save_path
        )
        
        if not result:
            raise HTTPException(status_code=404, detail="文件下载失败")
        
        return save_path
    except tos.exceptions.TosServerError as e:
        logger.error(f"TOS服务端错误: {e.message}, RequestID: {e.request_id}")
        raise HTTPException(status_code=e.status_code, detail=f"存储服务错误: {e.message}")
    except tos.exceptions.TosClientError as e:
        logger.error(f"TOS客户端错误: {e.message}")
        raise HTTPException(status_code=400, detail=f"请求错误: {e.message}")
    except Exception as e:
        logger.error(f"下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文件下载服务不可用")


# 同步TOS下载操作，将在线程池中执行
def _download_from_tos(file_name, local_path):
    try:
        client = tos.TosClientV2(access_key, access_secret, endpoint, region)
        client.get_object_to_file(bucket_name, file_name, local_path)
        return local_path
    except tos.exceptions.TosClientError as e:
        logger.error(f'下载客户端错误: {e.message}, 原因: {e.cause}')
        return None
    except tos.exceptions.TosServerError as e:
        logger.error(f'下载服务端错误: {e.code}, RequestID: {e.request_id}, 消息: {e.message}')
        return None
    except Exception as e:
        logger.error(f'下载未知错误: {e}')
        return None
    

# 同步列举TOS文件，将在线程池中执行
def _list_files_from_tos(prefix="", delimiter="", max_keys=1000):
    try:
        client = tos.TosClientV2(access_key, access_secret, endpoint, region)
        # 列举对象，可以指定前缀、分隔符和最大数量
        result = client.list_objects_type2(bucket_name, prefix=prefix, 
                                          delimiter=delimiter, max_keys=max_keys)
        
        files_list = []
        for item in result.contents:
            files_list.append(item.key)
            
        # 如果有更多结果，可以获取continuation_token继续列举
        has_more = result.is_truncated
        next_token = result.next_continuation_token if has_more else None
            
        return {
            "files": files_list,
            "has_more": has_more,
            "next_token": next_token
        }
    except tos.exceptions.TosClientError as e:
        logger.error(f'列举文件客户端错误: {e.message}, 原因: {e.cause}')
        return None
    except tos.exceptions.TosServerError as e:
        logger.error(f'列举文件服务端错误: {e.code}, RequestID: {e.request_id}, 消息: {e.message}')
        return None
    except Exception as e:
        logger.error(f'列举文件未知错误: {e}')
        return None

# 异步列举TOS文件
async def list_files_from_tos(prefix="", delimiter="", max_keys=1000):
    # 创建线程池执行同步操作
    with ThreadPoolExecutor() as executor:
        loop = asyncio.get_running_loop()
        result = await loop.run_in_executor(
            executor, 
            lambda: _list_files_from_tos(prefix, delimiter, max_keys)
        )
    return result

# 分页列举文件的方法（处理大量文件情况）
async def list_all_files_from_tos(prefix="", delimiter="", batch_size=1000):
    all_files = []
    next_token = None
    has_more = True
    
    while has_more:
        try:
            with ThreadPoolExecutor() as executor:
                loop = asyncio.get_running_loop()
                
                # 使用线程池执行同步TOS操作
                def _list_with_token():
                    try:
                        client = tos.TosClientV2(access_key, access_secret, endpoint, region)
                        params = {
                            "prefix": prefix,
                            "delimiter": delimiter,
                            "max_keys": batch_size
                        }
                        
                        # 添加continuation_token参数（如果有）
                        if next_token:
                            params["continuation_token"] = next_token
                            
                        result = client.list_objects_type2(bucket_name, **params)
                        return result
                    except Exception as e:
                        logger.error(f'批量列举文件错误: {e}')
                        return None
                
                result = await loop.run_in_executor(executor, _list_with_token)
                
                if not result:
                    break
                    
                # 添加当前批次的文件
                for item in result.contents:
                    all_files.append(item.key)
                
                # 检查是否还有更多文件
                has_more = result.is_truncated
                next_token = result.next_continuation_token if has_more else None
                
        except Exception as e:
            logger.error(f'列举所有文件错误: {e}')
            break
    
    return all_files

import os
import zipfile
import hashlib
import httpx
import shutil
import asyncio
from pathlib import Path

def get_material_source_download_folder():
    """Get the download folder path, similar to iOS implementation"""
    # Using current directory + sourceDownload as default location
    base_path = os.path.join(os.getcwd(), "all_materials")
    return base_path

async def download_zip_source(url: str, source: str, file_name: str):
    """
    从TOS下载zip文件并解密（与iOS downloadZipSourceFrom等效的Python实现）
    
    参数:
        url: 下载URL（实际上不会使用，因为我们从TOS下载）
        source: 源文件名（如果是加密文件，预期包含_encrypt）
        file_name: 保存的最终文件名
        
    返回:
        tuple: (final_path, final_name) 或失败时返回 (None, None)
    """
    folder_path = get_material_source_download_folder()
    source_basename = os.path.basename(source)
    zip_path = os.path.join(folder_path, source_basename)
    
    # 创建文件夹（如果不存在）
    os.makedirs(folder_path, exist_ok=True)
    
    # 检查是否为加密文件
    if "_encrypt" not in source:
        logger.info("非加密文件，跳过处理")
        return None, None
    
    # 只获取文件名部分（不包含路径）
    file_name_only = source_basename
    
    # 为zip生成MD5哈希密码（只使用文件名部分）
    zip_key = hashlib.md5(file_name_only.encode()).hexdigest()
    
    try:
        # 从TOS下载文件
        download_result = await download_file_async(source, zip_path)
        
        if not download_result or not os.path.exists(zip_path):
            logger.error(f"从TOS下载文件失败: {source}")
            return None, None
                    
        # 解压缩带密码的zip文件
        extracted_files = []
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # 获取zip中的文件列表
            file_list = zip_ref.namelist()
            # 解压所有文件
            zip_ref.extractall(path=folder_path, pwd=bytes(zip_key, 'utf-8'))
            # 记录解压的文件
            extracted_files = [os.path.join(folder_path, f) for f in file_list]
        
        # 解压后删除zip文件
        if os.path.exists(zip_path):
            os.remove(zip_path)

        # 检查是否有解压的文件
        if not extracted_files:
            logger.warning(f"解压后没有文件: {source}")
            return None, None
            
        # 获取第一个解压的文件
        extracted_path = extracted_files[0]
        extracted_ext = os.path.splitext(extracted_path)[1]
        
        # 根据原始zip文件名创建新的文件名（保留_encrypt部分）
        new_name = file_name_only  # 保留原始文件名（包含_encrypt）
        if extracted_ext:  # 如果解压文件有扩展名，使用它替换zip扩展名
            new_name = os.path.splitext(file_name_only)[0] + extracted_ext
            
        # 处理MP4文件的大小写转换
        if extracted_ext.upper() == '.MP4':
            new_name = os.path.splitext(new_name)[0] + extracted_ext.lower()
        
        new_path = os.path.join(folder_path, new_name)
        
        # 重命名解压的文件为新名称
        os.rename(extracted_path, new_path)
        
        return new_path, new_name
            
    except Exception as e:
        logger.error(f"下载或解压文件错误: {e}")
        return None, None

        logger.error(f"下载或解压文件错误: {e}")
        return None, None


