import os
import zipfile
import hashlib
import time
import shutil
import json
from pathlib import Path
import hashlib

# External library for password-protected zip files
# pip install pyzipper
import pyzipper

def sha1(text):
    """Calculate SHA1 hash of text"""
    return hashlib.sha1(text.encode('utf-8')).hexdigest()

def md5(text):
    """Calculate MD5 hash of text"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def get_timestamp_millis():
    """Get current timestamp in milliseconds"""
    return str(time.time() * 1000)

def create_zip_with_password(source_dir, output_zip, password):
    """Create a password-protected ZIP file"""
    # 使用更兼容的加密模式
    with pyzipper.AESZipFile(output_zip, 'w', compression=pyzipper.ZIP_DEFLATED, 
                             encryption=pyzipper.WZ_AES) as zf:
        zf.pwd = password.encode('utf-8')  # 直接设置密码
        
        # Walk through all files and directories in the source directory
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                # 使用更简单的相对路径计算
                arc_name = os.path.relpath(file_path, source_dir)
                zf.write(file_path, arcname=arc_name)

def create_liveroom_package(liveroom_path, userId, title, timestamp, room_id, output_dir, operation_id):
    """
    Create the final livebox package with nested encryption
    
    Args:
        liveroom_path: Path to the liveroom directory
        userId: User ID for password generation
        title: Title for password generation
        timestamp: Timestamp for password generation
        room_id: Room ID for password generation
        output_dir: Directory where the final zip will be placed
        operation_id: Unique identifier for this operation
        
    Returns:
        RoomConfig object with zip_path and other details
    """
    # Constants
    BACKUP_APP_KEY = "HBbZV^SMrwEz3VO@voiPJvqxn2hlgP56xndd"
    OUTER_PASSWORD = "T2OKOBNgXOasBm$q03iwOXQH12&9B%dhJqb%"
    
    # Generate the inner password for room folder
    room_key = md5(f"{room_id}_{userId}_{timestamp}").upper()
    password_key_base = md5(f"{title}_{room_id}_{timestamp}_{BACKUP_APP_KEY}").upper()
    password_key = sha1(password_key_base).upper()
    
    # Print passwords for reference (remove in production)
    print(f"Inner password (room): {password_key}")
    print(f"Outer password (liveroom): {OUTER_PASSWORD}")
    
    # Create temporary directory for packaging
    temp_dir = os.path.join(os.path.dirname(liveroom_path), "temp_packaging")
    os.makedirs(temp_dir, exist_ok=True)
    
    try:
        # 1. Create zip for room folder with a "room" folder structure
        room_path = os.path.join(liveroom_path, "room")
        room_zip_path = os.path.join(temp_dir, "room.zip")
        
        # 直接创建带有"room"目录结构的zip
        with pyzipper.AESZipFile(room_zip_path, 'w', compression=pyzipper.ZIP_DEFLATED, 
                                encryption=pyzipper.WZ_AES) as zf:
            zf.pwd = password_key.encode('utf-8')
            
            # Walk through all files in the room directory
            for root, dirs, files in os.walk(room_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 添加"room/"前缀，确保解压时创建room文件夹
                    rel_path = os.path.relpath(file_path, room_path)
                    arc_name = os.path.join("room", rel_path)
                    zf.write(file_path, arcname=arc_name)
        
        # Move room.zip to liveroom folder and delete room folder
        shutil.move(room_zip_path, os.path.join(liveroom_path, "room.zip"))
        shutil.rmtree(room_path)
        
        # 2. Create outer zip for liveroom folder - store in output_dir with unique name
        final_zip_name = f"liveroom_{operation_id}.zip"
        final_zip_path = os.path.join(output_dir, final_zip_name)
        
        create_zip_with_password(liveroom_path, final_zip_path, OUTER_PASSWORD)
        
        # Create a RoomConfig object to return
        # (assuming this is what your code expects based on your main function)
        class RoomConfig:
            def __init__(self, zip_path):
                self.zip_path = zip_path
        
        return RoomConfig(final_zip_path)
    
    finally:
        # Clean up temporary directory
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

