<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式AI对话客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            min-height: 80px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .output {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .reasoning {
            color: #6c757d;
            font-style: italic;
            margin-bottom: 5px;
        }
        .delta {
            color: #495057;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .model-tag {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>流式AI对话客户端</h1>
        
        <div class="input-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="http://localhost:5005" placeholder="http://localhost:5005">
        </div>
        
        <div class="input-group">
            <label for="modelSelect">模型选择:</label>
            <select id="modelSelect">
                <option value="1">DeepSeek R1 (不联网)</option>
                <option value="2">DeepSeek R1 (联网)</option>
                <option value="3">DeepSeek V3 (不联网)</option>
                <option value="4">DeepSeek V3 (联网)</option>
            </select>
        </div>
        
        <div class="input-group">
            <label for="systemPrompt">系统提示词:</label>
            <textarea id="systemPrompt" placeholder="可选，留空使用默认提示词">你是一个文生图提示词润色大师，能够根据用户输入的提示词，进行润色，使提示词更加生动、有趣、符合图片的场景。[请确保你的回答都是中文]</textarea>
        </div>
        
        <div class="input-group">
            <label for="question">用户问题:</label>
            <textarea id="question" placeholder="请输入您的问题...">一个美丽的女孩在海边</textarea>
        </div>
        
        <button id="startBtn" onclick="startStreaming()">开始对话</button>
        <button id="stopBtn" onclick="stopStreaming()" disabled>停止对话</button>
        <button onclick="clearOutput()">清空输出</button>
        
        <div id="status" class="status"></div>
        
        <div class="output" id="output"></div>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function appendOutput(text, className = '') {
            const output = document.getElementById('output');
            const span = document.createElement('span');
            span.textContent = text;
            if (className) {
                span.className = className;
            }
            output.appendChild(span);
            output.scrollTop = output.scrollHeight;
        }

        function startStreaming() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const embedModel = document.getElementById('modelSelect').value;
            const systemPrompt = document.getElementById('systemPrompt').value.trim();
            const question = document.getElementById('question').value.trim();

            if (!serverUrl || !question) {
                alert('请填写服务器地址和问题');
                return;
            }

            // 更新UI状态
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('正在连接...', 'connecting');

            // 准备请求数据
            const requestData = {
                embed_model: embedModel,
                question: question
            };

            if (systemPrompt) {
                requestData.system_prompt = systemPrompt;
            }

            // 发送POST请求开始流式连接
            fetch(`${serverUrl}/api/streamcomplete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                updateStatus('连接成功，开始接收数据...', 'connected');
                
                // 创建Reader来读取流
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            updateStatus('对话结束', 'connected');
                            resetUI();
                            return;
                        }

                        // 解析SSE数据
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.substring(6));
                                    handleSSEData(data);
                                } catch (e) {
                                    console.error('解析JSON失败:', e, line);
                                }
                            }
                        }
                        
                        // 继续读取
                        readStream();
                    }).catch(error => {
                        console.error('读取流时出错:', error);
                        updateStatus(`连接错误: ${error.message}`, 'error');
                        resetUI();
                    });
                }
                
                readStream();
            }).catch(error => {
                console.error('请求失败:', error);
                updateStatus(`连接失败: ${error.message}`, 'error');
                resetUI();
            });
        }

        function handleSSEData(data) {
            const modelTag = `[${data.model}]`;
            
            switch (data.type) {
                case 'chunk':
                    // 显示推理内容
                    if (data.reasoning_content) {
                        appendOutput(`${modelTag} `, 'model-tag');
                        appendOutput(`思考: ${data.reasoning_content}\n`, 'reasoning');
                    }
                    
                    // 显示增量内容
                    if (data.delta) {
                        appendOutput(data.delta, 'delta');
                    }
                    break;
                    
                case 'error':
                    appendOutput(`${modelTag} `, 'model-tag');
                    appendOutput(`错误: ${data.error}\n`, 'error');
                    break;
                    
                case 'end':
                    appendOutput(`\n${modelTag} 对话结束\n`, 'model-tag');
                    updateStatus('对话完成', 'connected');
                    resetUI();
                    break;
                    
                default:
                    console.log('未知数据类型:', data);
            }
        }

        function stopStreaming() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            updateStatus('连接已断开', 'error');
            resetUI();
        }

        function resetUI() {
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            isConnected = false;
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
            updateStatus('输出已清空', 'info');
        }

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', stopStreaming);
    </script>
</body>
</html> 