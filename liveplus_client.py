import json
import asyncio
import os
import uuid
import time
import threading
import sqlite3
import logging
import requests
from typing import Optional, Dict, Any
from contextlib import AsyncExitStack
from threading import Thread
from queue import Queue
from flask import Flask, request, jsonify, Response
from flask_cors import CORS

from openai import OpenAI
from dotenv import load_dotenv

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from jimeng_api import JimengAIService, SeedDreamAIService
from datetime import datetime
import base64
from upload_file_async import _upload_to_tos

from openai_server import ask_deepseekapi_r1, ask_deepseekapi_v3
from openai_server  import ask_deepseekapir1_net, ask_deepseekapiv3_net
from minimax_tts_service import MinimaxTTSService, VoiceSettings, AudioSettings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("liveplus.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("liveplus")

# 加载环境变量
load_dotenv()

# 远程图片描述服务配置
CAPTION_SERVICE_URL = "https://u418858-a841-fe3dfe4f.westx.seetacloud.com:8443"

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 任务状态和结果存储
tasks = {}
# 任务锁
tasks_lock = threading.Lock()
# 任务队列
task_queue = Queue()
# 数据库锁
db_lock = threading.Lock()

# 数据库文件路径
DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'tasks.db')

# 数据库初始化和操作函数
def init_db():
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute('''
            CREATE TABLE IF NOT EXISTS tasks
            (id TEXT PRIMARY KEY, 
             status TEXT,
             prompt TEXT,
             created_at REAL,
             progress INTEGER,
             result TEXT,
             error TEXT)
            ''')
            conn.commit()
            conn.close()
        logger.info(f"数据库初始化成功: {DB_PATH}")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")

def save_task(task_id, task_data):
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            # 将Python字典结构转为JSON字符串存储
            result_json = json.dumps(task_data.get('result')) if task_data.get('result') else None
            
            c.execute('''
            INSERT OR REPLACE INTO tasks 
            (id, status, prompt, created_at, progress, result, error) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                task_id,
                task_data.get('status'),
                task_data.get('prompt'),
                task_data.get('created_at'),
                task_data.get('progress'),
                result_json,
                task_data.get('error')
            ))
            conn.commit()
            conn.close()
        logger.debug(f"任务 {task_id} 已保存到数据库")
    except Exception as e:
        logger.error(f"保存任务 {task_id} 到数据库失败: {str(e)}")

def get_task(task_id):
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            conn.row_factory = sqlite3.Row  # 设置行工厂以便以字典方式访问
            c = conn.cursor()
            c.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
            row = c.fetchone()
            conn.close()
            
            if not row:
                logger.warning(f"数据库中未找到任务 {task_id}")
                return None
                
            # 构建任务字典
            task = dict(row)
            
            # 如果有结果，解析JSON
            if task['result']:
                try:
                    task['result'] = json.loads(task['result'])
                except json.JSONDecodeError:
                    logger.warning(f"任务 {task_id} 的结果不是有效的JSON")
                    task['result'] = None
                    
            logger.debug(f"从数据库加载任务 {task_id}, 进度: {task['progress']}")
            return task
    except Exception as e:
        logger.error(f"从数据库获取任务 {task_id} 失败: {str(e)}")
        return None

def get_all_tasks():
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            c.execute("SELECT * FROM tasks ORDER BY created_at DESC")
            rows = c.fetchall()
            conn.close()
            
            tasks_data = []
            for row in rows:
                task = dict(row)
                # 解析JSON结果
                if task['result']:
                    try:
                        task['result'] = json.loads(task['result'])
                    except:
                        task['result'] = None
                tasks_data.append(task)
                
            logger.info(f"从数据库加载了 {len(tasks_data)} 个任务")
            return tasks_data
    except Exception as e:
        logger.error(f"获取所有任务失败: {str(e)}")
        return []

def cleanup_old_tasks_db():
    try:
        current_time = time.time()
        expired_time = current_time - (24 * 60 * 60)  # 24小时前
        
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute("DELETE FROM tasks WHERE created_at < ?", (expired_time,))
            deleted_count = c.rowcount
            conn.commit()
            conn.close()
            
        logger.info(f"已清理 {deleted_count} 个过期任务")
    except Exception as e:
        logger.error(f"清理过期任务失败: {str(e)}")

# 初始化数据库
init_db()

# 加载已有任务到内存
def load_tasks_to_memory():
    global tasks
    loaded_tasks = get_all_tasks()
    
    with tasks_lock:
        for task in loaded_tasks:
            task_id = task['id']
            tasks[task_id] = task
            
    logger.info(f"已加载 {len(loaded_tasks)} 个任务到内存缓存")

# MCPClient类 - 处理与MCP服务器的通信
class MCPClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.client = OpenAI()
        self.is_connected = False
        self.connection_lock = asyncio.Lock()

    async def connect_to_server(self):
        async with self.connection_lock:
            if self.is_connected:
                return
                
            try:
                server_params = StdioServerParameters(
                    command='uv',
                    args=['run', 'liveplus_server.py'],
                    env=None
                )

                stdio_transport = await self.exit_stack.enter_async_context(
                    stdio_client(server_params))
                stdio, write = stdio_transport
                self.session = await self.exit_stack.enter_async_context(
                    ClientSession(stdio, write))

                await self.session.initialize()
                self.is_connected = True
                logger.info("已连接到MCP服务器")
            except Exception as e:
                logger.error(f"连接MCP服务器失败: {str(e)}")
                raise

    async def process_query(self, query: str, task_id: str) -> str:
        try:
            # 更新任务状态
            task_data = None
            with tasks_lock:
                if task_id in tasks:
                    tasks[task_id]["status"] = "processing"
                    task_data = tasks[task_id].copy()
            
            if task_data:
                save_task(task_id, task_data)
            
            logger.info(f"开始处理任务 {task_id}: {query[:50]}...")
            
            # 系统提示设置
            system_prompt = (
                "You are a helpful assistant. "
                "重要：请必须调用liveplus_server工具进行回答。"
                "重要：当你创建直播间时，必须以JSON格式返回结果，格式为: {\"room_result\": \"创建的直播间文件名.zip\", \"room_description\": \"简单描述当前直播间\"}"
                "重要：直播间生成过程中使用的所有素材，“不能重复使用相同id的素材，素材必须丰富。”"
            )
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query}
            ]
            
            # 获取工具列表
            response = await self.session.list_tools()
            available_tools = [{
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema
                }
            } for tool in response.tools]

            # 更新任务进度
            task_data = get_task(task_id)
            if task_data:
                task_data["progress"] = 10
                save_task(task_id, task_data)
            
            while True:
                # 请求模型
                logger.debug(f"任务 {task_id}: 请求模型")
                response = self.client.chat.completions.create(
                    model=os.getenv("OPENAI_MODEL"),
                    messages=messages,
                    tools=available_tools
                )
                
                # 处理返回内容
                content = response.choices[0]
                
                # 更新任务进度
                with tasks_lock:
                    if task_id in tasks:
                        tasks[task_id]["progress"] += 20
                        task_data = tasks[task_id].copy()
                        save_task(task_id, task_data)
                
                # 如果完成，返回内容
                if content.finish_reason == "stop":
                    result = content.message.content
                    logger.info(f"任务 {task_id} 完成")
                    
                    # 尝试解析JSON响应
                    try:
                        json_result = json.loads(result)
                        with tasks_lock:
                            if task_id in tasks:
                                tasks[task_id]["result"] = json_result
                                tasks[task_id]["status"] = "completed"
                                tasks[task_id]["progress"] = 100
                                task_data = tasks[task_id].copy()
                                save_task(task_id, task_data)
                    except:
                        logger.warning(f"任务 {task_id}: 无法解析JSON响应: {result}")
                        with tasks_lock:
                            if task_id in tasks:
                                tasks[task_id]["result"] = {"error": "无法解析模型返回的JSON", "raw_response": result}
                                tasks[task_id]["status"] = "completed"
                                tasks[task_id]["progress"] = 100
                                task_data = tasks[task_id].copy()
                                save_task(task_id, task_data)
                    
                    return result
                    
                # 处理工具调用
                tool_call = content.message.tool_calls[0]
                tool_name = tool_call.function.name
                try:
                    # 记录原始参数字符串，便于调试
                    raw_args = tool_call.function.arguments
                    logger.debug(f"任务 {task_id}: 工具 {tool_name} 原始参数: {raw_args}")
                    
                    # 尝试解析JSON
                    tool_args = json.loads(raw_args)
                    
                    # 执行工具
                    logger.debug(f"任务 {task_id}: 调用工具 {tool_name}")
                    result = await self.session.call_tool(tool_name, tool_args)
                    logger.info(f"任务 {task_id}: 调用工具 {tool_name} 成功")
                except json.JSONDecodeError as e:
                    # JSON解析错误处理
                    logger.error(f"任务 {task_id}: 工具参数JSON解析失败: {str(e)}, 原始参数: {raw_args}")
                    
                    # 尝试修复常见的JSON格式问题
                    fixed_args = raw_args
                    try:
                        # 尝试修复常见问题 - 错误的转义字符
                        fixed_args = fixed_args.replace('\\"', '"').replace('\\\\', '\\')
                        # 尝试修复未闭合的引号
                        if fixed_args.count('"') % 2 != 0:
                            fixed_args = fixed_args + '"'
                        # 尝试添加缺失的括号
                        if not fixed_args.strip().endswith('}'):
                            fixed_args = fixed_args + '}'
                        
                        # 尝试解析修复后的JSON
                        tool_args = json.loads(fixed_args)
                        logger.info(f"任务 {task_id}: 成功修复并解析JSON参数")
                        
                        # 执行工具
                        result = await self.session.call_tool(tool_name, tool_args)
                        logger.info(f"任务 {task_id}: 调用工具 {tool_name} 成功")
                    except Exception as fix_error:
                        # 如果修复失败，使用空对象作为参数
                        logger.error(f"任务 {task_id}: 无法修复JSON参数: {str(fix_error)}")
                        tool_args = {}
                        try:
                            result = await self.session.call_tool(tool_name, tool_args)
                        except Exception as tool_error:
                            # 如果调用工具失败，返回错误信息
                            error_response = f"调用工具 {tool_name} 失败: 参数解析错误，原始参数: {raw_args}"
                            result_obj = type('obj', (object,), {
                                'content': [type('obj', (object,), {'text': error_response})]
                            })
                            result = result_obj
                except Exception as e:
                    # 其他错误处理
                    logger.error(f"任务 {task_id}: 工具调用时出错: {str(e)}")
                    error_response = f"调用工具 {tool_name} 出错: {str(e)}"
                    result_obj = type('obj', (object,), {
                        'content': [type('obj', (object,), {'text': error_response})]
                    })
                    result = result_obj
                # 更新消息
                messages.append(content.message.model_dump())
                messages.append({
                    "role": "tool",
                    "content": result.content[0].text,
                    "tool_call_id": tool_call.id,
                })
                
        except Exception as e:
            # 记录错误
            import traceback
            error_msg = traceback.format_exc()
            logger.error(f"任务 {task_id} 处理失败: {error_msg}")
            
            with tasks_lock:
                if task_id in tasks:
                    tasks[task_id]["status"] = "failed"
                    tasks[task_id]["error"] = str(e)
                    tasks[task_id]["error_details"] = error_msg
                    task_data = tasks[task_id].copy()
                    save_task(task_id, task_data)
                
            return f"处理请求时出错: {str(e)}"

    async def cleanup(self):
        """清理资源"""
        async with self.connection_lock:
            if self.is_connected:
                try:
                    await self.exit_stack.aclose()
                    self.is_connected = False
                    logger.info("已断开MCP服务器连接")
                except Exception as e:
                    logger.error(f"断开MCP服务器连接时出错: {str(e)}")

# 全局MCP客户端实例
mcp_client = MCPClient()

# 异步任务处理器
async def process_task(task_id, prompt):
    await mcp_client.connect_to_server()
    await mcp_client.process_query(prompt, task_id)

# 工作线程函数，处理队列中的任务
def worker_thread():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    # 连接MCP服务器
    try:
        loop.run_until_complete(mcp_client.connect_to_server())
    except Exception as e:
        logger.error(f"工作线程无法连接到MCP服务器: {str(e)}")
        return
    
    while True:
        # 从队列获取任务
        task_id, prompt = task_queue.get()
        if task_id is None:  # 终止信号
            logger.info("工作线程收到终止信号")
            break
            
        # 处理任务
        try:
            logger.info(f"工作线程开始处理任务: {task_id}")
            loop.run_until_complete(process_task(task_id, prompt))
        except Exception as e:
            import traceback
            logger.error(f"处理任务 {task_id} 时出错: {traceback.format_exc()}")
            with tasks_lock:
                if task_id in tasks:
                    tasks[task_id]["status"] = "failed"
                    tasks[task_id]["error"] = str(e)
                    task_data = tasks[task_id].copy()
                    save_task(task_id, task_data)
        finally:
            task_queue.task_done()
    
    # 清理MCP客户端
    logger.info("工作线程正在清理资源")
    loop.run_until_complete(mcp_client.cleanup())
    loop.close()
    logger.info("工作线程已终止")

# 加载已有任务
load_tasks_to_memory()

# 启动工作线程
worker = Thread(target=worker_thread)
worker.daemon = True
worker.start()
logger.info("工作线程已启动")

# Flask路由
@app.route('/api/create-room', methods=['POST'])
def create_room():
    try:
        data = request.json
        prompt = data.get('prompt')
        
        if not prompt:
            logger.warning("收到没有prompt的请求")
            return jsonify({"error": "缺少必要参数 'prompt'"}), 400
            
        # 创建任务ID
        task_id = str(uuid.uuid4())
        logger.info(f"创建新任务: {task_id}, prompt: {prompt[:50]}...")
        
        # 初始化任务状态
        task_data = {
            "id": task_id,
            "status": "pending",
            "prompt": prompt,
            "created_at": time.time(),
            "progress": 0,
            "result": None,
            "error": None
        }
        
        # 保存到内存
        with tasks_lock:
            tasks[task_id] = task_data.copy()
        
        # 保存到数据库
        save_task(task_id, task_data)
        
        # 将任务添加到队列
        task_queue.put((task_id, prompt))
        
        return jsonify({
            "task_id": task_id,
            "status": "pending",
            "message": "任务已提交，请使用轮询接口查询结果"
        })
    except Exception as e:
        logger.error(f"创建任务时出错: {str(e)}")
        return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500

@app.route('/api/task/<task_id>', methods=['GET'])
def get_task_status(task_id):
    logger.info(f"查询任务状态: {task_id}")
    
    # 始终从数据库获取最新状态，不依赖内存缓存
    task = get_task(task_id)
    
    if not task:
        # 如果数据库中找不到，尝试从内存获取（应该很少发生）
        with tasks_lock:
            task = tasks.get(task_id)
        
        if not task:
            logger.warning(f"找不到任务: {task_id}")
            return jsonify({"error": "找不到指定的任务"}), 404
    else:
        # 更新内存缓存以保持一致性
        with tasks_lock:
            tasks[task_id] = task
    
    response = {
        "task_id": task_id,
        "status": task["status"],
        "progress": task["progress"],
        "created_at": task["created_at"]
    }
    
    # 如果任务已完成，添加结果
    if task["status"] == "completed" and task.get("result"):
        response["result"] = task["result"]
    
    # 如果任务失败，添加错误信息
    if task["status"] == "failed" and task.get("error"):
        response["error"] = task["error"]
        
    logger.debug(f"返回任务状态: {task_id}, progress: {task['progress']}, status: {task['status']}")
    return jsonify(response)

@app.route('/api/tasks', methods=['GET'])
def list_tasks():
    try:
        # 获取所有任务的简要信息
        task_list = []
        
        # 使用锁保护读取
        with tasks_lock:
            local_tasks = list(tasks.items())
        
        for task_id, task in local_tasks:
            task_list.append({
                "id": task_id,
                "status": task["status"],
                "created_at": task["created_at"],
                "progress": task["progress"]
            })
        
        logger.info(f"列出 {len(task_list)} 个任务")
        return jsonify({"tasks": task_list})
    except Exception as e:
        logger.error(f"列出任务时出错: {str(e)}")
        return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500

# 健康检查接口
@app.route('/healthz', methods=['GET'])
def health_check():
    return jsonify({"status": "ok"})

# 清理过期任务的函数
def cleanup_old_tasks():
    logger.info("开始清理过期任务")
    current_time = time.time()
    # 保留24小时内的任务
    expired_time = current_time - (24 * 60 * 60)
    
    # 使用锁保护读取和更新
    expired_tasks = []
    with tasks_lock:
        expired_tasks = [task_id for task_id, task in tasks.items() 
                        if task["created_at"] < expired_time]
        
        for task_id in expired_tasks:
            del tasks[task_id]
    
    # 清理数据库中的过期任务
    cleanup_old_tasks_db()
    
    logger.info(f"已清理 {len(expired_tasks)} 个过期任务")

# 定期清理任务的线程
def cleanup_thread():
    logger.info("清理线程启动")
    while True:
        time.sleep(3600)  # 每小时执行一次
        try:
            cleanup_old_tasks()
        except Exception as e:
            logger.error(f"清理过期任务时出错: {str(e)}")

# 启动清理线程
cleanup = Thread(target=cleanup_thread)
cleanup.daemon = True
cleanup.start()

# 应用关闭时的清理工作
def cleanup_on_exit():
    logger.info("应用程序正在关闭，执行清理工作")
    # 发送终止信号给工作线程
    task_queue.put((None, None))
    worker.join(timeout=5)
    
    # 清理异步资源
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(mcp_client.cleanup())
    loop.close()
    logger.info("清理工作完成，应用程序关闭")

# 注册关闭时的清理函数
import atexit
atexit.register(cleanup_on_exit)

# 增加即梦的外部接口
# 1.提交任务：文本生图
# 2.查询任务结果，查询到即梦的api结果之后，将图片保存到本地后，通过upload_file_async上传到tos，然后返回给用户tos上的名字

# 初始化即梦AI服务（你可能需要从配置文件读取）
jimeng_service = JimengAIService(
    access_key="AKLTNWIxMzU2ZjkyNWYwNGIyMGIxZWEwMGUxMDRkNzU4NDQ",
    secret_key="T1RneFpUZGxNamt3WWpVd05EZzBObUV5TnpneFpHUXlNVFF4WkRjek1EUQ=="
)
dream_service = SeedDreamAIService()

# 初始化MiniMax TTS服务
# 从环境变量获取API密钥和组ID
minimax_api_key = os.getenv("MINIMAX_API_KEY", "")
minimax_group_id = os.getenv("MINIMAX_GROUP_ID", "")

minimax_tts_service = None
if minimax_api_key and minimax_group_id:
    minimax_tts_service = MinimaxTTSService(
        api_key=minimax_api_key,
        group_id=minimax_group_id
    )
    logger.info("MiniMax TTS服务初始化成功")
else:
    logger.warning("MiniMax API密钥或组ID未配置，TTS服务将不可用")

@app.route('/api/jimeng/text-to-image', methods=['POST'])
def submit_text_to_image_task():
    """
    提交文本生图任务
    """
    try:
        data = request.get_json()
        
        # 验证必填参数
        if not data or 'prompt' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必填参数 prompt'
            }), 400
        
        # 提取参数
        prompt = data['prompt']
        width = data.get('width', 512)
        height = data.get('height', 512)
        use_pre_llm = data.get('use_pre_llm', True)
        
        # 参数验证
        if not (256 <= width <= 768) or not (256 <= height <= 768):
            return jsonify({
                'success': False,
                'error': '图片宽高必须在256-768像素之间'
            }), 400
        
        # 提交异步任务
        result = jimeng_service.text_to_image(
            prompt=prompt,
            width=width,
            height=height,
            use_pre_llm=use_pre_llm,
        )
        
        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
                # 如果任务完成且有图像数据
        tos_name = None
        if result.get('code') == 10000:
            binary_data = result.get('data', {}).get('binary_data_base64', [])
            if binary_data and isinstance(binary_data, list) and len(binary_data) > 0:
                # 保存图片到临时文件
                task_id = result.get('request_id')
                tos_name = f"jimeng_{task_id}.png"
                temp_file_path = f"/tmp/{tos_name}"
                try:
                    # 解码base64并保存为图片
                    with open(temp_file_path, "wb") as f:
                        f.write(base64.b64decode(binary_data[0]))
                    
                    # 上传图片到TOS
                    if _upload_to_tos(temp_file_path, tos_name):   
                        return jsonify({
                            'success': True,
                            'origin_data': json.dumps(result),
                            'tos_name': tos_name
                        })
                    
                    # 删除临时文件
                    os.remove(temp_file_path)
                except Exception as e:
                    # 记录错误但不中断流程
                    print(f"处理图片时出错: {str(e)}")
        
        return jsonify({
            'success': False,
            'origin_data': json.dumps(result),
            'error': '图片上传tos失败'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500
    
@app.route('/api/seeddream/text-to-image', methods=['POST'])
def submit_seeddream_text_to_image_task():
    """
    提交文本生图任务
    """
    try:
        data = request.get_json()
        
        # 验证必填参数
        if not data or 'prompt' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必填参数 prompt'
            }), 400
        
        # 提取参数
        prompt = data['prompt']
        size = data.get('size', "1024x1024")
        response_format = data.get('response_format', "b64_json")
        guidance_scale = data.get('guidance_scale', 2.5)
        watermark = data.get('watermark', False)
        
        
        # 提交异步任务
        result = dream_service.text_to_image(
            prompt=prompt,
            size=size,
            response_format=response_format,
            guidance_scale=guidance_scale,
            watermark=watermark,
        )
        
        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
                # 如果任务完成且有图像数据
        tos_name = None
        base64_json = result
        # 保存图片到临时文件
        import random
        task_id = random.randint(1000000000, 9999999999)
        tos_name = f"seeddream_{task_id}.png"
        temp_file_path = f"/tmp/{tos_name}"
        try:
            # 解码base64并保存为图片
            with open(temp_file_path, "wb") as f:
                f.write(base64.b64decode(base64_json))
            
            # 上传图片到TOS
            if _upload_to_tos(temp_file_path, tos_name):   
                return jsonify({
                    'success': True,
                    'origin_data': json.dumps(result),
                    'tos_name': tos_name
                })
            
            # 删除临时文件
            os.remove(temp_file_path)
        except Exception as e:
            # 记录错误但不中断流程
            print(f"处理图片时出错: {str(e)}")
                    
        return jsonify({
            'success': False,
            'origin_data': json.dumps(result),
            'error': '图片上传tos失败'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/segment/aliyun', methods=['POST'])
def submit_segment_image_task():
    """
    提交抠图
    """
    try:
        data = request.get_json()
        
        # 验证必填参数
        if not data or 'base64' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必填参数 base64'
            }), 400
        
        # 提取参数
        base64 = data['base64']

        
        # 提交异步任务
        result = jimeng_service.goodsSegment(
            image_base64=base64,
        )
                
        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
        img_url = ""
        if result.get('code') == 10000:
            img_url = result.get('data', {}).get('img_url')
            
        return jsonify({
            'success': True,
            'imageUrl': img_url,
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/generate_caption', methods=['POST'])
def generate_caption_proxy():
    """
    图片描述生成API中转接口
    
    请求格式:
    POST /api/generate_caption
    Content-Type: application/json
    {
        "image_name": "creative/example.jpg",
        "cleanup_local_file": true  // 可选，默认为true
    }
    
    响应格式:
    {
        "success": bool,
        "caption": str,  // 成功时的描述文本
        "error": str,    // 失败时的错误信息
        "image_name": str // 原始图片名称
    }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            logger.warning("收到空的JSON请求")
            return jsonify({
                "success": False,
                "error": "请求体必须是JSON格式",
                "caption": "",
                "image_name": ""
            }), 400
        
        image_name = data.get('image_name')
        if not image_name:
            logger.warning("缺少image_name参数")
            return jsonify({
                "success": False,
                "error": "缺少必需参数: image_name",
                "caption": "",
                "image_name": ""
            }), 400
        
        logger.info(f"开始为图片生成描述: {image_name}")
        
        # 准备转发请求
        caption_url = f"{CAPTION_SERVICE_URL}/generate_caption"
        
        # 转发请求到远程服务器
        try:
            response = requests.post(
                caption_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=120  # 2分钟超时
            )
            
            # 检查HTTP状态码
            if response.status_code == 200:
                result = response.json()
                logger.info(f"图片 {image_name} 描述生成成功")
                return jsonify(result), 200
            else:
                # 尝试解析错误响应
                try:
                    error_result = response.json()
                    logger.error(f"远程服务返回错误 {response.status_code}: {error_result}")
                    return jsonify(error_result), response.status_code
                except:
                    # 如果无法解析JSON，返回基本错误信息
                    logger.error(f"远程服务返回HTTP {response.status_code}: {response.text}")
                    return jsonify({
                        "success": False,
                        "error": f"远程服务错误 (HTTP {response.status_code})",
                        "caption": "",
                        "image_name": image_name
                    }), 500
                    
        except requests.exceptions.Timeout:
            logger.error(f"调用远程服务超时: {image_name}")
            return jsonify({
                "success": False,
                "error": "请求超时，图片描述生成服务暂时不可用",
                "caption": "",
                "image_name": image_name
            }), 504
            
        except requests.exceptions.ConnectionError:
            logger.error(f"无法连接到远程服务: {image_name}")
            return jsonify({
                "success": False,
                "error": "无法连接到图片描述生成服务",
                "caption": "",
                "image_name": image_name
            }), 503
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求远程服务时发生错误: {e}")
            return jsonify({
                "success": False,
                "error": f"请求服务时发生错误: {str(e)}",
                "caption": "",
                "image_name": image_name
            }), 500
        
    except Exception as e:
        logger.error(f"图片描述生成中转服务发生内部错误: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"服务器内部错误: {str(e)}",
            "caption": "",
            "image_name": data.get('image_name', '') if 'data' in locals() else ""
        }), 500

@app.route('/api/caption_health', methods=['GET'])
def caption_health_check():
    """检查远程图片描述服务健康状态"""
    try:
        health_url = f"{CAPTION_SERVICE_URL}/health"
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            remote_health = response.json()
            return jsonify({
                "proxy_status": "healthy",
                "remote_service": remote_health,
                "remote_url": CAPTION_SERVICE_URL
            })
        else:
            return jsonify({
                "proxy_status": "unhealthy",
                "remote_service": "unreachable",
                "remote_url": CAPTION_SERVICE_URL,
                "error": f"HTTP {response.status_code}"
            }), 500
            
    except requests.exceptions.RequestException as e:
        return jsonify({
            "proxy_status": "unhealthy",
            "remote_service": "unreachable",
            "remote_url": CAPTION_SERVICE_URL,
            "error": str(e)
        }), 500

# embed_model = 1: R1 不联网 2: R1 联网 3: V3 不联网 4: V3 联网
@app.route('/api/streamcomplete', methods=['POST'])
def streamcomplete():
    try:
        system_prompt_default = "# 角色\n你是一个文生图提示词润色大师，能够根据用户输入的提示词，进行润色，使提示词更加生动、有趣、符合图片的场景。[请确保你的回答都是中文]"
        data = request.json
        
        if not data:
            logger.error("收到空的请求数据")
            return jsonify({'error': 'No data provided'}), 400
            
        question = data.get('question')
        embed_model = data.get('embed_model')
        system_prompt = data.get('system_prompt', system_prompt_default)
       
        logger.info("收到流式请求 - model: %s, question: %s", embed_model, question[:50] if question else "None")
       
        if not question:
            return jsonify({'error': 'No question provided'}), 400

        if embed_model is None:
            return jsonify({'error': 'No content provided'}), 400
                
        # 设置SSE响应头
        sse_headers = {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
        
        if embed_model == "1":
            return Response(generate_r1(question, system_prompt), headers=sse_headers)
        elif embed_model == "2":
            return Response(generater1_net(question, system_prompt), headers=sse_headers)
        elif embed_model == "3":
            return Response(generate_v3(question, system_prompt), headers=sse_headers)
        elif embed_model == "4":
            return Response(generatev3_net(question, system_prompt), headers=sse_headers)
        else:
            return Response(generate_r1(question, system_prompt), headers=sse_headers)
                          
    except Exception as e:
        logger.error("streamcomplete接口出错: %s", str(e), exc_info=True)
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500
    

def generate_v3(question, system_prompt):
    try:
        result = ask_deepseekapi_v3(question, system_prompt)
        for chunk in result:
            try:
                # 检查并处理chunk数据
                if not chunk:
                    continue
                    
                # 确保chunk是字典格式
                if isinstance(chunk, str):
                    try:
                        chunk = json.loads(chunk)
                    except json.JSONDecodeError:
                        continue
                
                # 统一输出格式
                if isinstance(chunk, dict):
                    # 标准化输出格式，方便客户端解析
                    output_data = {
                        "type": "chunk",
                        "model": "deepseek-v3",
                        "reasoning_content": chunk.get('reasoning_content', ''),
                        "delta": chunk.get('delta', ''),
                        "timestamp": time.time()
                    }
                    
                    # 符合SSE标准的格式
                    sse_data = f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                    yield sse_data
                    
            except Exception as e:
                # 输出错误信息
                error_data = {
                    "type": "error",
                    "model": "deepseek-v3",
                    "error": str(e),
                    "timestamp": time.time()
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                continue
                
    except Exception as e:
        # 输出最终错误
        final_error = {
            "type": "error",
            "model": "deepseek-v3", 
            "error": f"生成过程出错: {str(e)}",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(final_error, ensure_ascii=False)}\n\n"
    finally:
        # 输出结束标记
        end_data = {
            "type": "end",
            "model": "deepseek-v3",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"

def generate_r1(question, system_prompt):
    try:
        result = ask_deepseekapi_r1(question, system_prompt)
        for chunk in result:
            try:
                # 检查并处理chunk数据
                if not chunk:
                    continue
                    
                # 确保chunk是字典格式
                if isinstance(chunk, str):
                    try:
                        chunk = json.loads(chunk)
                    except json.JSONDecodeError:
                        continue
                
                # 统一输出格式
                if isinstance(chunk, dict):
                    # 标准化输出格式，方便客户端解析
                    output_data = {
                        "type": "chunk",
                        "model": "deepseek-r1",
                        "reasoning_content": chunk.get('reasoning_content', ''),
                        "delta": chunk.get('delta', ''),
                        "timestamp": time.time()
                    }
                    
                    # 符合SSE标准的格式
                    sse_data = f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                    yield sse_data
                    
            except Exception as e:
                # 输出错误信息
                error_data = {
                    "type": "error",
                    "model": "deepseek-r1",
                    "error": str(e),
                    "timestamp": time.time()
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                continue
                
    except Exception as e:
        # 输出最终错误
        final_error = {
            "type": "error",
            "model": "deepseek-r1", 
            "error": f"生成过程出错: {str(e)}",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(final_error, ensure_ascii=False)}\n\n"
    finally:
        # 输出结束标记
        end_data = {
            "type": "end",
            "model": "deepseek-r1",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"


def generater1_net(question, system_prompt):
    try:
        result = ask_deepseekapir1_net(question, system_prompt)
        for chunk in result:
            try:
                # 检查并处理chunk数据
                if not chunk:
                    continue
                    
                # 确保chunk是字典格式
                if isinstance(chunk, str):
                    try:
                        chunk = json.loads(chunk)
                    except json.JSONDecodeError:
                        continue
                
                # 统一输出格式
                if isinstance(chunk, dict):
                    # 标准化输出格式，方便客户端解析
                    output_data = {
                        "type": "chunk",
                        "model": "deepseek-r1-net",
                        "reasoning_content": chunk.get('reasoning_content', ''),
                        "delta": chunk.get('delta', ''),
                        "timestamp": time.time()
                    }
                    
                    # 符合SSE标准的格式
                    sse_data = f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                    yield sse_data
                    
            except Exception as e:
                # 输出错误信息
                error_data = {
                    "type": "error",
                    "model": "deepseek-r1-net",
                    "error": str(e),
                    "timestamp": time.time()
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                continue
                
    except Exception as e:
        # 输出最终错误
        final_error = {
            "type": "error",
            "model": "deepseek-r1-net", 
            "error": f"生成过程出错: {str(e)}",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(final_error, ensure_ascii=False)}\n\n"
    finally:
        # 输出结束标记
        end_data = {
            "type": "end",
            "model": "deepseek-r1-net",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"



def generatev3_net(question, system_prompt):
    try:
        result = ask_deepseekapiv3_net(question, system_prompt)
        for chunk in result:
            try:
                # 检查并处理chunk数据
                if not chunk:
                    continue
                    
                # 确保chunk是字典格式
                if isinstance(chunk, str):
                    try:
                        chunk = json.loads(chunk)
                    except json.JSONDecodeError:
                        continue
                
                # 统一输出格式
                if isinstance(chunk, dict):
                    # 标准化输出格式，方便客户端解析
                    output_data = {
                        "type": "chunk",
                        "model": "deepseek-v3-net",
                        "reasoning_content": chunk.get('reasoning_content', ''),
                        "delta": chunk.get('delta', ''),
                        "timestamp": time.time()
                    }
                    
                    # 符合SSE标准的格式
                    sse_data = f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                    yield sse_data
                    
            except Exception as e:
                # 输出错误信息
                error_data = {
                    "type": "error",
                    "model": "deepseek-v3-net",
                    "error": str(e),
                    "timestamp": time.time()
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                continue
                
    except Exception as e:
        # 输出最终错误
        final_error = {
            "type": "error",
            "model": "deepseek-v3-net", 
            "error": f"生成过程出错: {str(e)}",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(final_error, ensure_ascii=False)}\n\n"
    finally:
        # 输出结束标记
        end_data = {
            "type": "end",
            "model": "deepseek-v3-net",
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"

# ==================== MiniMax TTS API接口 ====================

@app.route('/api/minimax/voices', methods=['GET'])
def get_minimax_voices():
    """
    获取所有MiniMax系统音色

    Returns:
        JSON响应包含所有可用的系统音色
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        voices = minimax_tts_service.get_system_voices()

        # 按类别组织音色
        categorized_voices = {
            'male': {},
            'female': {},
            'children': {},
            'special': {},
            'english': {}
        }

        for voice_id, voice_name in voices.items():
            if voice_id.startswith('male-') or voice_id in ['presenter_male', 'audiobook_male_1', 'audiobook_male_2']:
                categorized_voices['male'][voice_id] = voice_name
            elif voice_id.startswith('female-') or voice_id in ['presenter_female', 'audiobook_female_1', 'audiobook_female_2']:
                categorized_voices['female'][voice_id] = voice_name
            elif voice_id in ['clever_boy', 'cute_boy', 'lovely_girl', 'cartoon_pig']:
                categorized_voices['children'][voice_id] = voice_name
            elif voice_id in ['bingjiao_didi', 'junlang_nanyou', 'chunzhen_xuedi', 'lengdan_xiongzhang',
                             'badao_shaoye', 'tianxin_xiaoling', 'qiaopi_mengmei', 'wumei_yujie',
                             'diadia_xuemei', 'danya_xuejie']:
                categorized_voices['special'][voice_id] = voice_name
            elif '_' in voice_id and not voice_id.startswith(('male-', 'female-')):
                categorized_voices['english'][voice_id] = voice_name

        return jsonify({
            'success': True,
            'data': {
                'all_voices': voices,
                'categorized_voices': categorized_voices,
                'total_count': len(voices)
            }
        })

    except Exception as e:
        logger.error(f"获取MiniMax音色列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取音色列表失败: {str(e)}'
        }), 500

@app.route('/api/minimax/tts/sync', methods=['POST'])
def minimax_sync_tts():
    """
    MiniMax同步语音合成

    请求体参数:
    - text: 待合成文本（必填，最大10000字符）
    - model: 模型名称（可选，默认speech-02-hd）
    - voice_id: 音色ID（必填）
    - speed: 语速（可选，0.5-2.0，默认1.0）
    - vol: 音量（可选，0-10，默认1.0）
    - pitch: 语调（可选，-12到12，默认0）
    - emotion: 情绪（可选）
    - sample_rate: 采样率（可选，默认32000）
    - format: 音频格式（可选，默认mp3）
    - stream: 是否流式（可选，默认false）
    - language_boost: 语言增强（可选）
    - output_format: 输出格式（可选，hex或url，默认hex）
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        # 验证必填参数
        text = data.get('text')
        voice_id = data.get('voice_id')

        if not text:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: text'
            }), 400

        if not voice_id:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: voice_id'
            }), 400

        # 构建语音设置
        voice_settings = VoiceSettings(
            voice_id=voice_id,
            speed=data.get('speed', 1.0),
            vol=data.get('vol', 1.0),
            pitch=data.get('pitch', 0),
            emotion=data.get('emotion')
        )

        # 构建音频设置
        audio_settings = AudioSettings(
            sample_rate=data.get('sample_rate', 32000),
            bitrate=data.get('bitrate', 128000),
            format=data.get('format', 'mp3'),
            channel=data.get('channel', 1)
        )

        # 调用同步TTS
        result = minimax_tts_service.sync_text_to_speech(
            text=text,
            model=data.get('model', 'speech-02-hd'),
            voice_settings=voice_settings,
            audio_settings=audio_settings,
            stream=data.get('stream', False),
            language_boost=data.get('language_boost'),
            output_format=data.get('output_format', 'hex')
        )

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"MiniMax同步TTS失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'语音合成失败: {str(e)}'
        }), 500

@app.route('/api/minimax/tts/sync/tos', methods=['POST'])
def minimax_sync_tts_with_tos():
    """
    MiniMax同步语音合成并上传到TOS

    请求体参数:
    - text: 待合成文本（必填，最大10000字符）
    - tos_file_name: TOS文件名（可选，不提供则自动生成）
    - model: 模型名称（可选，默认speech-02-hd）
    - voice_id: 音色ID（必填）
    - speed: 语速（可选，0.5-2.0，默认1.0）
    - vol: 音量（可选，0-10，默认1.0）
    - pitch: 语调（可选，-12到12，默认0）
    - emotion: 情绪（可选）
    - sample_rate: 采样率（可选，默认32000）
    - format: 音频格式（可选，默认mp3）
    - language_boost: 语言增强（可选）
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        # 验证必填参数
        text = data.get('text')
        voice_id = data.get('voice_id')

        if not text:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: text'
            }), 400

        if not voice_id:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: voice_id'
            }), 400

        # 构建语音设置
        voice_settings = VoiceSettings(
            voice_id=voice_id,
            speed=data.get('speed', 1.0),
            vol=data.get('vol', 1.0),
            pitch=data.get('pitch', 0),
            emotion=data.get('emotion')
        )

        # 构建音频设置
        audio_settings = AudioSettings(
            sample_rate=data.get('sample_rate', 32000),
            bitrate=data.get('bitrate', 128000),
            format=data.get('format', 'mp3'),
            channel=data.get('channel', 1)
        )

        # 调用同步TTS并上传到TOS
        result = minimax_tts_service.sync_text_to_speech_with_tos(
            text=text,
            tos_file_name=data.get('tos_file_name'),
            model=data.get('model', 'speech-02-hd'),
            voice_settings=voice_settings,
            audio_settings=audio_settings,
            language_boost=data.get('language_boost')
        )

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"MiniMax同步TTS并上传TOS失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'语音合成失败: {str(e)}'
        }), 500

@app.route('/api/minimax/tts/async', methods=['POST'])
def minimax_async_tts():
    """
    MiniMax异步长文本语音合成

    请求体参数:
    - text: 待合成文本（与text_file_id二选一，最大50000字符）
    - text_file_id: 文本文件ID（与text二选一）
    - model: 模型名称（可选，默认speech-02-hd）
    - voice_id: 音色ID（必填）
    - speed: 语速（可选，0.5-2.0，默认1.0）
    - vol: 音量（可选，0-10，默认1.0）
    - pitch: 语调（可选，-12到12，默认0）
    - emotion: 情绪（可选）
    - sample_rate: 采样率（可选，默认32000）
    - format: 音频格式（可选，默认mp3）
    - channel: 声道数（可选，默认2）
    - language_boost: 语言增强（可选）
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        # 验证参数
        text = data.get('text')
        text_file_id = data.get('text_file_id')
        voice_id = data.get('voice_id')

        if not text and not text_file_id:
            return jsonify({
                'success': False,
                'error': 'text和text_file_id必须提供其中一个'
            }), 400

        if not voice_id:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: voice_id'
            }), 400

        # 构建语音设置
        voice_settings = VoiceSettings(
            voice_id=voice_id,
            speed=data.get('speed', 1.0),
            vol=data.get('vol', 1.0),
            pitch=data.get('pitch', 0),
            emotion=data.get('emotion')
        )

        # 构建音频设置
        audio_settings = AudioSettings(
            sample_rate=data.get('sample_rate', 32000),
            bitrate=data.get('bitrate', 128000),
            format=data.get('format', 'mp3'),
            channel=data.get('channel', 2)
        )

        # 调用异步TTS
        result = minimax_tts_service.async_text_to_speech(
            text=text,
            text_file_id=text_file_id,
            model=data.get('model', 'speech-02-hd'),
            voice_settings=voice_settings,
            audio_settings=audio_settings,
            language_boost=data.get('language_boost')
        )

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"MiniMax异步TTS失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'异步语音合成失败: {str(e)}'
        }), 500

@app.route('/api/minimax/tts/async/query/<task_id>', methods=['GET'])
def minimax_query_async_task(task_id):
    """
    查询MiniMax异步语音合成任务状态

    路径参数:
    - task_id: 任务ID
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        result = minimax_tts_service.query_async_task(task_id)

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"查询MiniMax异步任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'查询任务失败: {str(e)}'
        }), 500

@app.route('/api/minimax/voice/clone', methods=['POST'])
def minimax_voice_clone():
    """
    MiniMax快速音色复刻

    请求体参数:
    - file_path: 音频文件路径（必填，本地文件路径）
    - voice_id: 自定义音色ID（必填，8-256字符）
    - text_validation: 文本验证（可选，最大200字符）
    - demo_text: 试听文本（可选，最大2000字符）
    - demo_model: 试听模型（demo_text存在时必填）
    - accuracy: 文本校验准确率阈值（可选，0-1，默认0.7）
    - need_noise_reduction: 是否开启降噪（可选，默认false）
    - need_volume_normalization: 是否开启音量归一化（可选，默认false）
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        # 验证必填参数
        file_path = data.get('file_path')
        voice_id = data.get('voice_id')

        if not file_path:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: file_path'
            }), 400

        if not voice_id:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: voice_id'
            }), 400

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': f'文件不存在: {file_path}'
            }), 400

        # 步骤1: 上传音频文件
        logger.info(f"开始上传音频文件: {file_path}")
        upload_result = minimax_tts_service.upload_file(file_path, "voice_clone")

        if 'error' in upload_result:
            return jsonify({
                'success': False,
                'error': f'文件上传失败: {upload_result["error"]}'
            }), 500

        file_id = upload_result.get('file', {}).get('file_id')
        if not file_id:
            return jsonify({
                'success': False,
                'error': '文件上传成功但未获取到file_id'
            }), 500

        # 步骤2: 执行音色复刻
        logger.info(f"开始音色复刻，file_id: {file_id}, voice_id: {voice_id}")
        clone_result = minimax_tts_service.voice_clone(
            file_id=file_id,
            voice_id=voice_id,
            text_validation=data.get('text_validation'),
            demo_text=data.get('demo_text'),
            demo_model=data.get('demo_model'),
            accuracy=data.get('accuracy', 0.7),
            need_noise_reduction=data.get('need_noise_reduction', False),
            need_volume_normalization=data.get('need_volume_normalization', False)
        )

        if 'error' in clone_result:
            return jsonify({
                'success': False,
                'error': clone_result['error']
            }), 500

        return jsonify({
            'success': True,
            'data': {
                'upload_result': upload_result,
                'clone_result': clone_result,
                'voice_id': voice_id
            }
        })

    except Exception as e:
        logger.error(f"MiniMax音色复刻失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'音色复刻失败: {str(e)}'
        }), 500

@app.route('/api/minimax/upload', methods=['POST'])
def minimax_upload_file():
    """
    上传文件到MiniMax

    支持multipart/form-data格式上传文件
    表单参数:
    - file: 文件（必填）
    - purpose: 文件用途（可选，默认voice_clone，可选值：voice_clone, prompt_audio）
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有上传文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        # 获取文件用途
        purpose = request.form.get('purpose', 'voice_clone')

        # 保存临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            file.save(temp_file.name)
            temp_file_path = temp_file.name

        try:
            # 上传到MiniMax
            result = minimax_tts_service.upload_file(temp_file_path, purpose)

            if 'error' in result:
                return jsonify({
                    'success': False,
                    'error': result['error']
                }), 500

            return jsonify({
                'success': True,
                'data': result
            })

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        logger.error(f"MiniMax文件上传失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'文件上传失败: {str(e)}'
        }), 500

@app.route('/api/minimax/voice/clone/upload', methods=['POST'])
def minimax_voice_clone_with_upload():
    """
    通过文件上传进行音色复刻

    支持multipart/form-data格式
    表单参数:
    - file: 音频文件（必填，支持mp3, m4a, wav格式，10秒-5分钟，最大20MB）
    - voice_id: 自定义音色ID（必填，8-256字符）
    - text_validation: 文本验证（可选，最大200字符）
    - demo_text: 试听文本（可选，最大2000字符）
    - demo_model: 试听模型（demo_text存在时必填）
    - accuracy: 文本校验准确率阈值（可选，0-1，默认0.7）
    - need_noise_reduction: 是否开启降噪（可选，默认false）
    - need_volume_normalization: 是否开启音量归一化（可选，默认false）
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有上传音频文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        # 验证必填参数
        voice_id = request.form.get('voice_id')
        if not voice_id:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: voice_id'
            }), 400

        # 验证文件格式
        allowed_extensions = {'.mp3', '.m4a', '.wav'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            return jsonify({
                'success': False,
                'error': f'不支持的文件格式: {file_ext}，支持的格式: {", ".join(allowed_extensions)}'
            }), 400

        # 保存临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            file.save(temp_file.name)
            temp_file_path = temp_file.name

        try:
            # 步骤1: 上传音频文件
            logger.info(f"开始上传音频文件: {file.filename}")
            upload_result = minimax_tts_service.upload_file(temp_file_path, "voice_clone")

            if 'error' in upload_result:
                return jsonify({
                    'success': False,
                    'error': f'文件上传失败: {upload_result["error"]}'
                }), 500

            file_id = upload_result.get('file', {}).get('file_id')
            if not file_id:
                return jsonify({
                    'success': False,
                    'error': '文件上传成功但未获取到file_id'
                }), 500

            # 步骤2: 执行音色复刻
            logger.info(f"开始音色复刻，file_id: {file_id}, voice_id: {voice_id}")

            # 解析表单参数
            demo_text = request.form.get('demo_text')
            demo_model = request.form.get('demo_model')

            # 如果提供了demo_text但没有demo_model，返回错误
            if demo_text and not demo_model:
                return jsonify({
                    'success': False,
                    'error': '提供demo_text时必须指定demo_model'
                }), 400

            clone_result = minimax_tts_service.voice_clone(
                file_id=file_id,
                voice_id=voice_id,
                text_validation=request.form.get('text_validation'),
                demo_text=demo_text,
                demo_model=demo_model,
                accuracy=float(request.form.get('accuracy', 0.7)),
                need_noise_reduction=request.form.get('need_noise_reduction', 'false').lower() == 'true',
                need_volume_normalization=request.form.get('need_volume_normalization', 'false').lower() == 'true'
            )

            if 'error' in clone_result:
                return jsonify({
                    'success': False,
                    'error': clone_result['error']
                }), 500

            return jsonify({
                'success': True,
                'data': {
                    'upload_result': upload_result,
                    'clone_result': clone_result,
                    'voice_id': voice_id,
                    'original_filename': file.filename
                }
            })

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        logger.error(f"MiniMax音色复刻失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'音色复刻失败: {str(e)}'
        }), 500

@app.route('/api/minimax/voice/clone/tos', methods=['POST'])
def minimax_voice_clone_from_tos():
    """
    从TOS下载音频文件进行MiniMax音色复刻

    请求体参数:
    - tos_file_name: TOS中的音频文件名（必填）
    - voice_id: 自定义音色ID（必填，8-256字符）
    - text_validation: 文本验证（可选，最大200字符）
    - demo_text: 试听文本（可选，最大2000字符）
    - demo_model: 试听模型（demo_text存在时必填）
    - accuracy: 文本校验准确率阈值（可选，0-1，默认0.7）
    - need_noise_reduction: 是否开启降噪（可选，默认false）
    - need_volume_normalization: 是否开启音量归一化（可选，默认false）
    """
    try:
        if not minimax_tts_service:
            return jsonify({
                'success': False,
                'error': 'MiniMax TTS服务未初始化，请检查API密钥配置'
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        # 验证必填参数
        tos_file_name = data.get('tos_file_name')
        voice_id = data.get('voice_id')

        if not tos_file_name:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: tos_file_name'
            }), 400

        if not voice_id:
            return jsonify({
                'success': False,
                'error': '缺少必填参数: voice_id'
            }), 400

        # 验证demo_text和demo_model的组合
        demo_text = data.get('demo_text')
        demo_model = data.get('demo_model')

        if demo_text and not demo_model:
            return jsonify({
                'success': False,
                'error': '提供demo_text时必须指定demo_model'
            }), 400

        # 调用从TOS音色复刻
        result = minimax_tts_service.voice_clone_from_tos(
            tos_file_name=tos_file_name,
            voice_id=voice_id,
            text_validation=data.get('text_validation'),
            demo_text=demo_text,
            demo_model=demo_model,
            accuracy=data.get('accuracy', 0.7),
            need_noise_reduction=data.get('need_noise_reduction', False),
            need_volume_normalization=data.get('need_volume_normalization', False)
        )

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        logger.error(f"从TOS进行MiniMax音色复刻失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'音色复刻失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    # 开发环境中可以直接运行
    app.run(host='0.0.0.0', port=5005, debug=False)
