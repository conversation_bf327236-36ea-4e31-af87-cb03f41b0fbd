# 数据库文件
*.db
*.sqlite
*.sqlite3
tasks.db
# Python虚拟环境
.venv/
venv/
ENV/
env/
# Python缓存文件
__pycache__/
*.py[cod]
*.class
.pytest_cache/
.coverage
htmlcov/
# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production
# 日志文件
*.log
logs/
liveplus.log
# 备份文件
*.bak
*.backup
*.swp
*~
liveplus_client.py.bak*
# 临时文件和目录
tmp/
temp/
.DS_Store
# IDE配置文件
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
# 生成的文件
*.zip
*.gz
backups/
# 缓存目录
.cache/
.sass-cache/
.npm/
.config/
# 其他
node_modules/
dist/
build/
