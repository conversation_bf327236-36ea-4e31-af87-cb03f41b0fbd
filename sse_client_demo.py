#!/usr/bin/env python3
"""
流式AI对话客户端 - Python版本
演示如何使用改进后的SSE流式API
"""

import json
import requests
from typing import Iterator, Dict, Any
import time

class StreamClient:
    def __init__(self, base_url: str = "http://localhost:5005"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def stream_complete(self, 
                       question: str, 
                       embed_model: str = "1", 
                       system_prompt: str = None) -> Iterator[Dict[Any, Any]]:
        """
        发送流式完成请求
        
        Args:
            question: 用户问题
            embed_model: 模型选择 (1:R1不联网, 2:R1联网, 3:V3不联网, 4:V3联网)
            system_prompt: 系统提示词（可选）
            
        Yields:
            Dict: 解析后的SSE数据
        """
        url = f"{self.base_url}/api/streamcomplete"
        
        # 准备请求数据
        data = {
            "embed_model": embed_model,
            "question": question
        }
        
        if system_prompt:
            data["system_prompt"] = system_prompt
            
        # 发送请求
        try:
            response = self.session.post(
                url,
                json=data,
                headers={'Content-Type': 'application/json'},
                stream=True,
                timeout=300  # 5分钟超时
            )
            
            response.raise_for_status()
            
            # 处理流式响应
            buffer = ""
            for chunk in response.iter_content(chunk_size=8192, decode_unicode=True):
                if chunk:
                    buffer += chunk
                    
                    # 处理完整的SSE消息
                    while '\n\n' in buffer:
                        message, buffer = buffer.split('\n\n', 1)
                        
                        for line in message.split('\n'):
                            if line.startswith('data: '):
                                try:
                                    data = json.loads(line[6:])  # 去掉 "data: " 前缀
                                    yield data
                                except json.JSONDecodeError as e:
                                    print(f"JSON解析错误: {e}, 原始数据: {line}")
                                    continue
                                    
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            yield {
                "type": "error",
                "error": str(e),
                "timestamp": time.time()
            }

def main():
    """主函数 - 演示客户端使用"""
    client = StreamClient("http://localhost:5005")
    
    print("=== 流式AI对话客户端 ===")
    print("模型选择:")
    print("1 - DeepSeek R1 (不联网)")
    print("2 - DeepSeek R1 (联网)")
    print("3 - DeepSeek V3 (不联网)")
    print("4 - DeepSeek V3 (联网)")
    print()
    
    # 获取用户输入
    model = input("请选择模型 (1-4，默认1): ").strip() or "1"
    question = input("请输入您的问题: ").strip()
    
    if not question:
        print("问题不能为空！")
        return
        
    # 可选的系统提示词
    use_custom_prompt = input("是否使用自定义系统提示词？(y/N): ").strip().lower()
    system_prompt = None
    if use_custom_prompt in ['y', 'yes']:
        system_prompt = input("请输入系统提示词: ").strip()
    
    print(f"\n开始对话... (模型: {model})")
    print("=" * 50)
    
    try:
        reasoning_content = ""
        response_content = ""
        
        for data in client.stream_complete(question, model, system_prompt):
            if data.get("type") == "chunk":
                model_name = data.get("model", "unknown")
                
                # 处理推理内容
                if data.get("reasoning_content"):
                    reasoning_content += data["reasoning_content"]
                    print(f"[{model_name}] 思考: {data['reasoning_content']}", end="", flush=True)
                
                # 处理增量响应
                if data.get("delta"):
                    response_content += data["delta"]
                    print(data["delta"], end="", flush=True)
                    
            elif data.get("type") == "error":
                print(f"\n❌ 错误: {data.get('error', '未知错误')}")
                
            elif data.get("type") == "end":
                model_name = data.get("model", "unknown")
                print(f"\n\n✅ [{model_name}] 对话结束")
                break
                
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断对话")
    except Exception as e:
        print(f"\n\n❌ 客户端错误: {e}")
    
    print("=" * 50)
    print("对话完成")

if __name__ == "__main__":
    main() 