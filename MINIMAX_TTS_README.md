# MiniMax TTS API 集成文档

本项目已集成 MiniMax 开放平台的语音合成功能，支持同步语音合成、异步长文本语音合成、系统音色管理和快速音色复刻。

## 功能特性

- ✅ **同步语音合成**: 支持最大 10000 字符的实时语音合成
- ✅ **异步长文本语音合成**: 支持最大 50000 字符的长文本语音生成
- ✅ **系统音色管理**: 获取 100+系统音色，包含男性、女性、儿童、特色和英文音色
- ✅ **快速音色复刻**: 基于音频文件快速复刻自定义音色
- ✅ **TOS 集成**: 支持从 TOS 下载音频进行复刻，合成语音自动上传到 TOS
- ✅ **多种音频格式**: 支持 MP3、PCM、FLAC、WAV 格式输出
- ✅ **流式输出**: 支持流式语音合成
- ✅ **情绪控制**: 支持 7 种情绪（高兴、悲伤、愤怒、害怕、厌恶、惊讶、中性）
- ✅ **多语言支持**: 支持 24 种全球广泛使用的语言

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加 MiniMax API 配置：

```bash
# MiniMax API配置
MINIMAX_API_KEY=your_minimax_api_key_here
MINIMAX_GROUP_ID=your_group_id_here
```

### 2. 获取 API 密钥

1. 访问 [MiniMax 开放平台](https://platform.minimaxi.com)
2. 注册并登录账户
3. 在"账户管理 > 接口密钥"中获取 API 密钥
4. 在相关页面获取组 ID (GroupId)

## API 接口说明

### 1. 获取系统音色列表

**接口**: `GET /api/minimax/voices`

**响应示例**:

```json
{
  "success": true,
  "data": {
    "all_voices": {
      "male-qn-qingse": "青涩青年音色",
      "female-tianmei": "甜美女性音色",
      ...
    },
    "categorized_voices": {
      "male": {...},
      "female": {...},
      "children": {...},
      "special": {...},
      "english": {...}
    },
    "total_count": 50
  }
}
```

### 2. 同步语音合成

**接口**: `POST /api/minimax/tts/sync`

**请求参数**:

```json
{
  "text": "待合成的文本",
  "model": "speech-02-hd",
  "voice_id": "female-tianmei",
  "speed": 1.0,
  "vol": 1.0,
  "pitch": 0,
  "emotion": "happy",
  "sample_rate": 32000,
  "format": "mp3",
  "stream": false,
  "language_boost": "auto",
  "output_format": "hex"
}
```

**参数说明**:

- `text`: 待合成文本（必填，最大 10000 字符）
- `model`: 模型名称（可选，默认 speech-02-hd）
- `voice_id`: 音色 ID（必填）
- `speed`: 语速（可选，0.5-2.0，默认 1.0）
- `vol`: 音量（可选，0-10，默认 1.0）
- `pitch`: 语调（可选，-12 到 12，默认 0）
- `emotion`: 情绪（可选，支持 happy/sad/angry/fearful/disgusted/surprised/calm）
- `sample_rate`: 采样率（可选，默认 32000）
- `format`: 音频格式（可选，默认 mp3）
- `stream`: 是否流式（可选，默认 false）
- `language_boost`: 语言增强（可选）
- `output_format`: 输出格式（可选，hex 或 url，默认 hex）

### 3. 同步语音合成并上传到 TOS

**接口**: `POST /api/minimax/tts/sync/tos`

**请求参数**:

```json
{
  "text": "待合成的文本",
  "tos_file_name": "custom_audio.mp3",
  "model": "speech-02-hd",
  "voice_id": "female-tianmei",
  "speed": 1.0,
  "vol": 1.0,
  "pitch": 0,
  "emotion": "happy",
  "sample_rate": 32000,
  "format": "mp3",
  "language_boost": "auto"
}
```

**参数说明**:

- `text`: 待合成文本（必填，最大 10000 字符）
- `tos_file_name`: TOS 文件名（可选，不提供则自动生成）
- 其他参数与同步语音合成接口相同

**响应示例**:

```json
{
  "success": true,
  "data": {
    "tos_file_name": "tts_sync_abc123def456.mp3",
    "extra_info": {
      "audio_length": 5746,
      "audio_size": 100845,
      "usage_characters": 30
    },
    "trace_id": "01b8bf9bb7433cc75c18eee6cfa8fe21"
  }
}
```

### 4. 异步长文本语音合成

**接口**: `POST /api/minimax/tts/async`

**请求参数**:

```json
{
  "text": "长文本内容...",
  "model": "speech-02-hd",
  "voice_id": "male-qn-qingse",
  "speed": 1.0,
  "vol": 1.0,
  "pitch": 0,
  "sample_rate": 32000,
  "format": "mp3",
  "channel": 2,
  "language_boost": "auto"
}
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "task_id": "95157322514444",
    "task_token": "eyJhbGciOiJSUz...",
    "file_id": 95157322514444
  }
}
```

### 4. 查询异步任务状态

**接口**: `GET /api/minimax/tts/async/query/{task_id}`

**响应示例**:

```json
{
  "success": true,
  "data": {
    "task_id": "95157322514444",
    "base_resp": {
      "status_code": 0,
      "status_msg": "success"
    },
    "file_id": 95157322514444
  }
}
```

### 5. 快速音色复刻（文件上传）

**接口**: `POST /api/minimax/voice/clone/upload`

**请求格式**: `multipart/form-data`

**表单参数**:

- `file`: 音频文件（必填，支持 mp3/m4a/wav，10 秒-5 分钟，最大 20MB）
- `voice_id`: 自定义音色 ID（必填，8-256 字符）
- `text_validation`: 文本验证（可选，最大 200 字符）
- `demo_text`: 试听文本（可选，最大 2000 字符）
- `demo_model`: 试听模型（demo_text 存在时必填）
- `accuracy`: 文本校验准确率阈值（可选，0-1，默认 0.7）
- `need_noise_reduction`: 是否开启降噪（可选，默认 false）
- `need_volume_normalization`: 是否开启音量归一化（可选，默认 false）

**响应示例**:

```json
{
  "success": true,
  "data": {
    "upload_result": {...},
    "clone_result": {
      "input_sensitive": false,
      "demo_audio": "试听音频链接",
      "base_resp": {
        "status_code": 0,
        "status_msg": "success"
      }
    },
    "voice_id": "TestVoice001",
    "original_filename": "audio.mp3"
  }
}
```

### 6. 从 TOS 进行音色复刻

**接口**: `POST /api/minimax/voice/clone/tos`

**请求参数**:

```json
{
  "tos_file_name": "audio_sample.mp3",
  "voice_id": "MyCustomVoice001",
  "text_validation": "这是验证文本",
  "demo_text": "这是试听文本",
  "demo_model": "speech-02-hd",
  "accuracy": 0.7,
  "need_noise_reduction": false,
  "need_volume_normalization": false
}
```

**参数说明**:

- `tos_file_name`: TOS 中的音频文件名（必填）
- `voice_id`: 自定义音色 ID（必填，8-256 字符）
- `text_validation`: 文本验证（可选，最大 200 字符）
- `demo_text`: 试听文本（可选，最大 2000 字符）
- `demo_model`: 试听模型（demo_text 存在时必填）
- `accuracy`: 文本校验准确率阈值（可选，0-1，默认 0.7）
- `need_noise_reduction`: 是否开启降噪（可选，默认 false）
- `need_volume_normalization`: 是否开启音量归一化（可选，默认 false）

**响应示例**:

```json
{
  "success": true,
  "data": {
    "tos_file_name": "audio_sample.mp3",
    "voice_id": "MyCustomVoice001",
    "upload_result": {...},
    "clone_result": {
      "input_sensitive": false,
      "demo_audio": "试听音频链接",
      "base_resp": {
        "status_code": 0,
        "status_msg": "success"
      }
    }
  }
}
```

### 7. 文件上传

**接口**: `POST /api/minimax/upload`

**请求格式**: `multipart/form-data`

**表单参数**:

- `file`: 文件（必填）
- `purpose`: 文件用途（可选，默认 voice_clone，可选值：voice_clone, prompt_audio）

## 使用示例

### Python 示例

```python
import requests

# 1. 获取系统音色
response = requests.get("http://localhost:5005/api/minimax/voices")
voices = response.json()

# 2. 同步语音合成
tts_data = {
    "text": "你好，这是MiniMax语音合成测试。",
    "voice_id": "female-tianmei",
    "emotion": "happy"
}
response = requests.post("http://localhost:5005/api/minimax/tts/sync", json=tts_data)
result = response.json()

# 3. 同步语音合成并上传到TOS
tts_tos_data = {
    "text": "这是TOS集成测试",
    "voice_id": "female-tianmei",
    "tos_file_name": "my_audio.mp3"
}
response = requests.post("http://localhost:5005/api/minimax/tts/sync/tos", json=tts_tos_data)
tos_result = response.json()

# 4. 从TOS进行音色复刻
clone_tos_data = {
    "tos_file_name": "source_audio.mp3",
    "voice_id": "MyTOSVoice001",
    "demo_text": "这是试听文本",
    "demo_model": "speech-02-hd"
}
response = requests.post("http://localhost:5005/api/minimax/voice/clone/tos", json=clone_tos_data)
clone_tos_result = response.json()

# 5. 音色复刻（文件上传）
with open("audio.mp3", "rb") as f:
    files = {"file": f}
    data = {"voice_id": "MyCustomVoice001"}
    response = requests.post(
        "http://localhost:5005/api/minimax/voice/clone/upload",
        files=files,
        data=data
    )
    clone_result = response.json()
```

### cURL 示例

```bash
# 获取音色列表
curl -X GET "http://localhost:5005/api/minimax/voices"

# 同步语音合成
curl -X POST "http://localhost:5005/api/minimax/tts/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "你好，这是测试文本",
    "voice_id": "female-tianmei",
    "emotion": "happy"
  }'

# 同步语音合成并上传到TOS
curl -X POST "http://localhost:5005/api/minimax/tts/sync/tos" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是TOS集成测试",
    "voice_id": "female-tianmei",
    "tos_file_name": "my_audio.mp3"
  }'

# 从TOS进行音色复刻
curl -X POST "http://localhost:5005/api/minimax/voice/clone/tos" \
  -H "Content-Type: application/json" \
  -d '{
    "tos_file_name": "source_audio.mp3",
    "voice_id": "MyTOSVoice001",
    "demo_text": "这是试听文本",
    "demo_model": "speech-02-hd"
  }'

# 音色复刻（文件上传）
curl -X POST "http://localhost:5005/api/minimax/voice/clone/upload" \
  -F "file=@audio.mp3" \
  -F "voice_id=MyVoice001"
```

## 测试工具

项目提供了完整的测试脚本 `test_minimax_tts.py`：

```bash
# 运行基础测试
python test_minimax_tts.py

# 运行包含音色复刻的完整测试
# python test_minimax_tts.py（需要修改脚本中的音频文件路径）
```

## 注意事项

1. **API 密钥安全**: 请妥善保管 API 密钥，不要提交到版本控制系统
2. **文件格式**: 音色复刻支持 mp3、m4a、wav 格式，文件大小不超过 20MB
3. **音频时长**: 复刻音频时长应在 10 秒到 5 分钟之间
4. **voice_id 规范**: 自定义音色 ID 长度 8-256 字符，首字符必须是字母，只允许字母、数字、-、_，末位不能是-或_
5. **并发限制**: 请注意 API 的并发限制和频率限制
6. **错误处理**: 建议在生产环境中添加适当的错误处理和重试机制

## 支持的模型

- `speech-02-hd`: 持续更新的 HD 模型，音质表现突出
- `speech-02-turbo`: 持续更新的 Turbo 模型，性能表现出色
- `speech-01-hd`: 稳定版本的 HD 模型，复刻相似度高
- `speech-01-turbo`: 稳定版本的 Turbo 模型，生成速度快

## 支持的语言

支持 24 种语言：中文、粤语、英语、西班牙语、法语、俄语、德语、葡萄牙语、阿拉伯语、意大利语、日语、韩语、印尼语、越南语、土耳其语、荷兰语、乌克兰语、泰语、波兰语、罗马尼亚语、希腊语、捷克语、芬兰语、印地语。
