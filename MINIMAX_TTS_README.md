# MiniMax TTS API 集成文档

本项目已集成MiniMax开放平台的语音合成功能，支持同步语音合成、异步长文本语音合成、系统音色管理和快速音色复刻。

## 功能特性

- ✅ **同步语音合成**: 支持最大10000字符的实时语音合成
- ✅ **异步长文本语音合成**: 支持最大50000字符的长文本语音生成
- ✅ **系统音色管理**: 获取100+系统音色，包含男性、女性、儿童、特色和英文音色
- ✅ **快速音色复刻**: 基于音频文件快速复刻自定义音色
- ✅ **多种音频格式**: 支持MP3、PCM、FLAC、WAV格式输出
- ✅ **流式输出**: 支持流式语音合成
- ✅ **情绪控制**: 支持7种情绪（高兴、悲伤、愤怒、害怕、厌恶、惊讶、中性）
- ✅ **多语言支持**: 支持24种全球广泛使用的语言

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加MiniMax API配置：

```bash
# MiniMax API配置
MINIMAX_API_KEY=your_minimax_api_key_here
MINIMAX_GROUP_ID=your_group_id_here
```

### 2. 获取API密钥

1. 访问 [MiniMax开放平台](https://platform.minimaxi.com)
2. 注册并登录账户
3. 在"账户管理 > 接口密钥"中获取API密钥
4. 在相关页面获取组ID (GroupId)

## API接口说明

### 1. 获取系统音色列表

**接口**: `GET /api/minimax/voices`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "all_voices": {
      "male-qn-qingse": "青涩青年音色",
      "female-tianmei": "甜美女性音色",
      ...
    },
    "categorized_voices": {
      "male": {...},
      "female": {...},
      "children": {...},
      "special": {...},
      "english": {...}
    },
    "total_count": 50
  }
}
```

### 2. 同步语音合成

**接口**: `POST /api/minimax/tts/sync`

**请求参数**:
```json
{
  "text": "待合成的文本",
  "model": "speech-02-hd",
  "voice_id": "female-tianmei",
  "speed": 1.0,
  "vol": 1.0,
  "pitch": 0,
  "emotion": "happy",
  "sample_rate": 32000,
  "format": "mp3",
  "stream": false,
  "language_boost": "auto",
  "output_format": "hex"
}
```

**参数说明**:
- `text`: 待合成文本（必填，最大10000字符）
- `model`: 模型名称（可选，默认speech-02-hd）
- `voice_id`: 音色ID（必填）
- `speed`: 语速（可选，0.5-2.0，默认1.0）
- `vol`: 音量（可选，0-10，默认1.0）
- `pitch`: 语调（可选，-12到12，默认0）
- `emotion`: 情绪（可选，支持happy/sad/angry/fearful/disgusted/surprised/calm）
- `sample_rate`: 采样率（可选，默认32000）
- `format`: 音频格式（可选，默认mp3）
- `stream`: 是否流式（可选，默认false）
- `language_boost`: 语言增强（可选）
- `output_format`: 输出格式（可选，hex或url，默认hex）

### 3. 异步长文本语音合成

**接口**: `POST /api/minimax/tts/async`

**请求参数**:
```json
{
  "text": "长文本内容...",
  "model": "speech-02-hd",
  "voice_id": "male-qn-qingse",
  "speed": 1.0,
  "vol": 1.0,
  "pitch": 0,
  "sample_rate": 32000,
  "format": "mp3",
  "channel": 2,
  "language_boost": "auto"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "95157322514444",
    "task_token": "eyJhbGciOiJSUz...",
    "file_id": 95157322514444
  }
}
```

### 4. 查询异步任务状态

**接口**: `GET /api/minimax/tts/async/query/{task_id}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "95157322514444",
    "base_resp": {
      "status_code": 0,
      "status_msg": "success"
    },
    "file_id": 95157322514444
  }
}
```

### 5. 快速音色复刻（文件上传）

**接口**: `POST /api/minimax/voice/clone/upload`

**请求格式**: `multipart/form-data`

**表单参数**:
- `file`: 音频文件（必填，支持mp3/m4a/wav，10秒-5分钟，最大20MB）
- `voice_id`: 自定义音色ID（必填，8-256字符）
- `text_validation`: 文本验证（可选，最大200字符）
- `demo_text`: 试听文本（可选，最大2000字符）
- `demo_model`: 试听模型（demo_text存在时必填）
- `accuracy`: 文本校验准确率阈值（可选，0-1，默认0.7）
- `need_noise_reduction`: 是否开启降噪（可选，默认false）
- `need_volume_normalization`: 是否开启音量归一化（可选，默认false）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "upload_result": {...},
    "clone_result": {
      "input_sensitive": false,
      "demo_audio": "试听音频链接",
      "base_resp": {
        "status_code": 0,
        "status_msg": "success"
      }
    },
    "voice_id": "TestVoice001",
    "original_filename": "audio.mp3"
  }
}
```

### 6. 文件上传

**接口**: `POST /api/minimax/upload`

**请求格式**: `multipart/form-data`

**表单参数**:
- `file`: 文件（必填）
- `purpose`: 文件用途（可选，默认voice_clone，可选值：voice_clone, prompt_audio）

## 使用示例

### Python示例

```python
import requests

# 1. 获取系统音色
response = requests.get("http://localhost:5005/api/minimax/voices")
voices = response.json()

# 2. 同步语音合成
tts_data = {
    "text": "你好，这是MiniMax语音合成测试。",
    "voice_id": "female-tianmei",
    "emotion": "happy"
}
response = requests.post("http://localhost:5005/api/minimax/tts/sync", json=tts_data)
result = response.json()

# 3. 音色复刻
with open("audio.mp3", "rb") as f:
    files = {"file": f}
    data = {"voice_id": "MyCustomVoice001"}
    response = requests.post(
        "http://localhost:5005/api/minimax/voice/clone/upload",
        files=files,
        data=data
    )
    clone_result = response.json()
```

### cURL示例

```bash
# 获取音色列表
curl -X GET "http://localhost:5005/api/minimax/voices"

# 同步语音合成
curl -X POST "http://localhost:5005/api/minimax/tts/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "你好，这是测试文本",
    "voice_id": "female-tianmei",
    "emotion": "happy"
  }'

# 音色复刻
curl -X POST "http://localhost:5005/api/minimax/voice/clone/upload" \
  -F "file=@audio.mp3" \
  -F "voice_id=MyVoice001"
```

## 测试工具

项目提供了完整的测试脚本 `test_minimax_tts.py`：

```bash
# 运行基础测试
python test_minimax_tts.py

# 运行包含音色复刻的完整测试
# python test_minimax_tts.py（需要修改脚本中的音频文件路径）
```

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要提交到版本控制系统
2. **文件格式**: 音色复刻支持mp3、m4a、wav格式，文件大小不超过20MB
3. **音频时长**: 复刻音频时长应在10秒到5分钟之间
4. **voice_id规范**: 自定义音色ID长度8-256字符，首字符必须是字母，只允许字母、数字、-、_，末位不能是-或_
5. **并发限制**: 请注意API的并发限制和频率限制
6. **错误处理**: 建议在生产环境中添加适当的错误处理和重试机制

## 支持的模型

- `speech-02-hd`: 持续更新的HD模型，音质表现突出
- `speech-02-turbo`: 持续更新的Turbo模型，性能表现出色
- `speech-01-hd`: 稳定版本的HD模型，复刻相似度高
- `speech-01-turbo`: 稳定版本的Turbo模型，生成速度快

## 支持的语言

支持24种语言：中文、粤语、英语、西班牙语、法语、俄语、德语、葡萄牙语、阿拉伯语、意大利语、日语、韩语、印尼语、越南语、土耳其语、荷兰语、乌克兰语、泰语、波兰语、罗马尼亚语、希腊语、捷克语、芬兰语、印地语。
