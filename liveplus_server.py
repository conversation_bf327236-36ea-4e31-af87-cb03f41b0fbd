from typing import Any, List, Tuple, Dict
import httpx
from mcp.server.fastmcp import FastMCP
import asyncio
from dataclasses import dataclass
from typing import List
from download_utils import download_zip_source
import json
import os
import shutil
from zip_utils import create_liveroom_package, get_timestamp_millis
import time
from upload_file_async import upload_file_async
import requests

# 引入火山引擎签名相关包
from volcengine.auth.SignerV4 import SignerV4
from volcengine.base.Request import Request
from volcengine.Credentials import Credentials

# 初始化mcp fastmcp server
mcp = FastMCP("liveplus", log_level="ERROR")

# 下载的资源地址 http://dev-liveapi.quickleading.com/resource/creative/ 后面拼接素材的名字

# 测试：未命名场景1xxx_023220.zip 
# 外层密码: T2OKOBNgXOasBm$q03iwOXQH12&9B%dhJqb%
# 内层密码: 160A6AABC9205A85244C326C955E5ABA7A9E3196

MATERIAL_HOST = "http://dev-liveapi.quickleading.com/resource/creative/"
USER_ID = "2577706"
OneBG_OneFG_TITLE = "mcp_generation_room_onebg_onefg"

# 火山引擎知识库配置
COLLECTION_NAME = "liveplus_material"
PROJECT_NAME = "default"
AK = "AKLTNWIxMzU2ZjkyNWYwNGIyMGIxZWEwMGUxMDRkNzU4NDQ"
SK = "T1RneFpUZGxNamt3WWpVd05EZzBObUV5TnpneFpHUXlNVFF4WkRjek1EUQ=="
KNOWLEDGE_BASE_DOMAIN = "api-knowledgebase.ml_platform.cn-beijing.volces.com"
ACCOUNT_ID = "**********"

"""
主体流程
1、用户通过描述，例如：我想搭建一个冬季室外的直播间
2、提交给大模型
3、大模型需要根据用户的描述，通过知识库获取合适的素材列表
4、拿到合适的图片之后，调用MCP服务
"""
@dataclass
class Material:
    # 素材id
    id: int
    # 素材名称
    name: str
    # 是否适用于背景素材
    background: bool 
    # 描述
    desc: str

def prepare_request(method, path, params=None, data=None, doseq=0):
    """
    准备知识库API请求并添加签名
    """
    if params:
        for key in params:
            if (
                    isinstance(params[key], int)
                    or isinstance(params[key], float)
                    or isinstance(params[key], bool)
            ):
                params[key] = str(params[key])
            elif isinstance(params[key], list):
                if not doseq:
                    params[key] = ",".join(params[key])
    r = Request()
    r.set_shema("http")
    r.set_method(method)
    r.set_connection_timeout(10)
    r.set_socket_timeout(10)
    mheaders = {
        "Accept": "application/json",
        "Content-Type": "application/json; charset=utf-8",
        "Host": KNOWLEDGE_BASE_DOMAIN,
        "V-Account-Id": ACCOUNT_ID,
    }
    r.set_headers(mheaders)
    if params:
        r.set_query(params)
    r.set_host(KNOWLEDGE_BASE_DOMAIN)
    r.set_path(path)
    if data is not None:
        r.set_body(json.dumps(data))

    # 生成签名
    credentials = Credentials(AK, SK, "air", "cn-north-1")
    SignerV4.sign(r, credentials)
    return r

async def search_knowledge(query_text):
    """
    搜索火山引擎知识库（异步版本）
    """
    method = "POST"
    path = "/api/knowledge/collection/search_knowledge"
    request_params = {
        "project": PROJECT_NAME,
        "name": COLLECTION_NAME,
        "query": query_text,
        "limit": 20,
        "pre_processing": {
            "need_instruction": True,
            "return_token_usage": True,
            "messages": [
                {
                    "role": "system",
                    "content": "请根据用户描述找到合适的直播间素材。每个素材都应包含id、name和background（是否为背景）属性。"
                },
                {
                    "role": "user"
                }
            ]
        },
        "dense_weight": 0.5,
        "post_processing": {
            "get_attachment_link": True,
            "rerank_only_chunk": False,
            "rerank_switch": False
        }
    }
    
    info_req = prepare_request(method=method, path=path, data=request_params)
    
    # 使用 aiohttp 替代 requests 进行异步请求
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.request(
                method=info_req.method,
                url=f"http://{KNOWLEDGE_BASE_DOMAIN}{info_req.path}",
                headers=info_req.headers,
                data=info_req.body,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as rsp:
                text = await rsp.text()
                try:
                    return json.loads(text)
                except:
                    return {"error": "无法解析知识库返回结果", "raw": text}
    except Exception as e:
        return {"error": f"知识库查询出错: {str(e)}", "raw": ""}

async def download_material(material: Material) -> Tuple[str, str]:
    """
    下载素材并返回本地路径
  
    参数:
        material: 包含名称和其他信息的Material对象
      
    返回:
        tuple: (local_path, file_name) 或失败时返回 (None, None)
    """
    # 源URL
    source_url = "creative/" + material.name
  
    # 判断是否为加密文件（根据命名约定）
    if "_encrypt" in material.name:
        source_name = material.name  # 应该包含 _encrypt
        # 通过移除 _encrypt（如果存在）来提取最终文件名
        final_name = material.name.replace("_encrypt", "")
    else:
        source_name = material.name
        final_name = material.name
  
    # 下载文件
    result = await download_zip_source(source_url, source_name, final_name)
    return result

@mcp.tool()
async def get_materials(description: str, numMaterialCount: int) -> str:
    """
    异步方法：根据描述从知识库获取直播间可用的素材列表
    
    参数:
        description: 用户对直播间的描述
        numMaterialCount: 需要获取的素材数量
    返回:
        匹配的素材列表(JSON字符串格式)
    """
    try:
        # 查询知识库获取素材列表
        query_text = f"请根据以下描述提供适合的直播间素材列表：{description}"
        
        # 使用异步方法查询知识库
        kb_response = await search_knowledge(query_text)
        
        # 解析知识库返回的数据
        materials = []
        if "data" in kb_response and "result_list" in kb_response["data"]:
            # 提取知识库检索结果
            result_list = kb_response["data"]["result_list"]
            
            for result in result_list:
                # 从table_chunk_fields中提取素材信息
                if "table_chunk_fields" in result:
                    material_data = {}
                    for field in result["table_chunk_fields"]:
                        field_name = field.get("field_name")
                        field_value = field.get("field_value")
                        if field_name and field_value is not None:
                            material_data[field_name] = field_value
                    
                    # 确保必要的字段存在
                    if "id" in material_data and "name" in material_data and "background" in material_data:
                        # 直接创建字典而不是Material对象
                        material = {
                            "id": int(material_data["id"]),
                            "name": material_data["name"],
                            "background": bool(material_data["background"]),
                            "desc": material_data.get("desc", "")  # 添加描述字段
                        }
                        materials.append(material)
            
            print(f"从知识库中提取了 {len(materials)} 个素材")

            import random
            # 打乱素材列表的顺序
            random.shuffle(materials)
            materials = materials[:numMaterialCount]
            
            # 将材料列表转换为JSON字符串
            return json.dumps(materials, ensure_ascii=False)

        else:
            # 如果返回格式不符合预期，返回空JSON数组
            print("知识库返回格式不符合预期")
            return "[]"
    except Exception as e:
        # 如果发生错误，记录异常并返回空JSON数组
        print(f"解析知识库响应时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return "[]"

@mcp.tool()
async def generateOneBackgroundAndOneForeground(background: Material, 
                                                foreground: Material) -> str:
    """
    总计需要2个素材。

    创建一个包含一个背景层和一个前景层的直播间;
    每个素材的id不应该是一样的。必须是不同的素材。
    确保传入的数据为Material结构，而不是字符串。

    参数:
        background: 背景层素材 背景图片，适用于用户描述的直播间场景的背景
        foreground: 前景层素材 前景图片，搭配背景使用的前景图 不能与背景图一样
    """
    # 为此操作创建唯一的工作目录
    import uuid
    operation_id = uuid.uuid4().hex
    base_dir = os.path.join(os.getcwd(), "mcp_output")
    work_dir = os.path.join(base_dir, f"mcp_work_{operation_id}")
    os.makedirs(base_dir, exist_ok=True)
    os.makedirs(work_dir, exist_ok=True)
  
    try:
        # 下载素材
        bg_path, bg_name = await download_material(background)
        fg_path, fg_name = await download_material(foreground)
      
        if not bg_path or not fg_path:
            return "Failed to download required materials"
      
        # 创建具有唯一路径的文件夹结构
        liveroom_path = os.path.join(work_dir, "liveroom")
        room_path = os.path.join(liveroom_path, "room")
        res_path = os.path.join(room_path, "res")
      
        # 创建目录
        os.makedirs(res_path, exist_ok=True)
      
        # 将素材复制到res文件夹
        shutil.copy(bg_path, os.path.join(res_path, bg_name))
        shutil.copy(fg_path, os.path.join(res_path, fg_name))
      
        # 更新room_onebg_onefg.json并另存为room.json
        with open("./room_onebg_onefg.json", "r", encoding="utf-8") as f:
            room_json = json.load(f)
      
        # 在JSON中替换素材名称
        room_json = json.dumps(room_json).replace("bg_source_name", bg_name).replace("fg_source_name", fg_name)
        room_json = json.loads(room_json)
      
        # 保存为room.json
        with open(os.path.join(room_path, "room.json"), "w", encoding="utf-8") as f:
            json.dump(room_json, f, indent=2)
      
        # 获取时间戳
        timestamp = get_timestamp_millis()
      
        # 从room.json获取roomId
        room_json_path = os.path.join(liveroom_path, "room", "room.json")
        with open(room_json_path, 'r') as f:
            room_data = json.load(f)
            room_id = room_data.get("roomId", "default_room_id")

        # 创建config.json
        create_config_json(liveroom_path, timestamp=timestamp,roomId=room_id,userId=USER_ID,title=OneBG_OneFG_TITLE)
      
        # 创建最终的打包zip文件，并进行嵌套加密
        # 输出文件应该放在base_dir中，而不是埋在工作目录中
        roomConfig = create_liveroom_package(liveroom_path, userId=USER_ID, title=OneBG_OneFG_TITLE, timestamp=timestamp, room_id=room_id, output_dir=base_dir, operation_id=operation_id)

        file_basename = os.path.basename(roomConfig.zip_path)
        remote_name = f"{file_basename}"
        # 执行异步上传
        upload_success = await upload_file_async(file_path=roomConfig.zip_path, remote_name=remote_name)
        if not upload_success:
            return "上传文件失败"
      
        return remote_name
  
    finally:
        # 完成后清理临时工作目录
        # 这确保我们不会留下临时文件
        if os.path.exists(work_dir):
            shutil.rmtree(work_dir)

@mcp.tool()
async def generateOneBackgroundAndThreeForeground(background: Material, 
                                                foreground1: Material, 
                                                foreground2: Material, 
                                                foreground3: Material) -> str:
    """
    总计需要4个素材。

    创建一个包含一个背景层、三个前景层的直播间;
    每个素材的id不应该是一样的。必须是不同的素材。
    确保传入的数据为Material结构，而不是字符串。

    参数:
        background: 背景层素材 背景图片，适用于用户描述的直播间场景的背景
        foreground1: 第一个前景层素材 前景图片，搭配背景使用的前景图 不能与背景图一样
        foreground2: 第二个前景层素材 前景图片，搭配背景使用的前景图 不能与背景图一样
        foreground3: 第三个前景层素材 前景图片，搭配背景使用的前景图 不能与背景图一样
    """
    # 为此操作创建唯一的工作目录
    import uuid
    operation_id = uuid.uuid4().hex
    base_dir = os.path.join(os.getcwd(), "mcp_output")
    work_dir = os.path.join(base_dir, f"mcp_work_{operation_id}")
    os.makedirs(base_dir, exist_ok=True)
    os.makedirs(work_dir, exist_ok=True)
  
    try:
        # 下载素材
        bg_path, bg_name = await download_material(background)
        fg_path1, fg_name1 = await download_material(foreground1)
        fg_path2, fg_name2 = await download_material(foreground2)
        fg_path3, fg_name3 = await download_material(foreground3)

        if not bg_path or not fg_path1 or not fg_path2 or not fg_path3:
            return "Failed to download required materials"
      
        # 创建具有唯一路径的文件夹结构
        liveroom_path = os.path.join(work_dir, "liveroom")
        room_path = os.path.join(liveroom_path, "room")
        res_path = os.path.join(room_path, "res")
      
        # 创建目录
        os.makedirs(res_path, exist_ok=True)
      
        # 将素材复制到res文件夹
        shutil.copy(bg_path, os.path.join(res_path, bg_name))
        shutil.copy(fg_path1, os.path.join(res_path, fg_name1))
        shutil.copy(fg_path2, os.path.join(res_path, fg_name2))
        shutil.copy(fg_path3, os.path.join(res_path, fg_name3))

        # 更新room_onebg_onefg.json并另存为room.json
        with open("./room_onebg_threefg.json", "r", encoding="utf-8") as f:
            room_json = json.load(f)
      
        # 在JSON中替换素材名称
        room_json = json.dumps(room_json).replace("bg_source_name", bg_name).replace("fg_source_name1", fg_name1).replace("fg_source_name2", fg_name2).replace("fg_source_name3", fg_name3)
        room_json = json.loads(room_json)
      
        # 保存为room.json
        with open(os.path.join(room_path, "room.json"), "w", encoding="utf-8") as f:
            json.dump(room_json, f, indent=2)
      
        # 获取时间戳
        timestamp = get_timestamp_millis()
      
        # 从room.json获取roomId 
        room_json_path = os.path.join(liveroom_path, "room", "room.json")
        with open(room_json_path, 'r') as f:
            room_data = json.load(f)
            room_id = room_data.get("roomId", "default_room_id")

        # 创建config.json
        create_config_json(liveroom_path, timestamp=timestamp,roomId=room_id,userId=USER_ID,title=OneBG_OneFG_TITLE)
      
        # 创建最终的打包zip文件，并进行嵌套加密
        # 输出文件应该放在base_dir中，而不是埋在工作目录中
        roomConfig = create_liveroom_package(liveroom_path, userId=USER_ID, title=OneBG_OneFG_TITLE, timestamp=timestamp, room_id=room_id, output_dir=base_dir, operation_id=operation_id)

        file_basename = os.path.basename(roomConfig.zip_path)
        remote_name = f"{file_basename}"
        # 执行异步上传
        upload_success = await upload_file_async(file_path=roomConfig.zip_path, remote_name=remote_name)
        if not upload_success:
            return "上传文件失败"
      
        return remote_name
  
    finally:
        # 完成后清理临时工作目录
        # 这确保我们不会留下临时文件
        if os.path.exists(work_dir):
            shutil.rmtree(work_dir)

@mcp.tool()
async def generateTwoBackgroundAndPerOneForeground(background1: Material, 
                                                   background2: Material, 
                                                foreground1: Material, 
                                                foreground2: Material) -> str:
    """
    总计需要5个素材。

    创建一个包含2个背景层、3个前景层的直播间;
    每个素材的id不应该是一样的。必须是不同的素材。
    确保传入的数据为Material结构，而不是字符串。

    参数:
        background1: 背景层第一个素材 背景图片，适用于用户描述的直播间场景的背景
        background2: 背景层第一个素材 背景图片，适用于用户描述的直播间场景的背景
        foreground1: 第一个前景层素材 前景图片，搭配背景使用的前景图 不能与背景图一样
        foreground2: 第二个前景层素材 前景图片，搭配背景使用的前景图 不能与背景图一样
    """
    # 为此操作创建唯一的工作目录
    import uuid
    operation_id = uuid.uuid4().hex
    base_dir = os.path.join(os.getcwd(), "mcp_output")
    work_dir = os.path.join(base_dir, f"mcp_work_{operation_id}")
    os.makedirs(base_dir, exist_ok=True)
    os.makedirs(work_dir, exist_ok=True)
  
    try:
        # 下载素材
        bg_path1, bg_name1 = await download_material(background1)
        bg_path2, bg_name2 = await download_material(background2)
        fg_path1, fg_name1 = await download_material(foreground1)
        fg_path2, fg_name2 = await download_material(foreground2)

        if not bg_path1 or not bg_path2 or not fg_path1 or not fg_path2:
            return "Failed to download required materials"
      
        # 创建具有唯一路径的文件夹结构
        liveroom_path = os.path.join(work_dir, "liveroom")
        room_path = os.path.join(liveroom_path, "room")
        res_path = os.path.join(room_path, "res")
      
        # 创建目录
        os.makedirs(res_path, exist_ok=True)
      
        # 将素材复制到res文件夹
        shutil.copy(bg_path1, os.path.join(res_path, bg_name1))
        shutil.copy(bg_path2, os.path.join(res_path, bg_name2))
        shutil.copy(fg_path1, os.path.join(res_path, fg_name1))
        shutil.copy(fg_path2, os.path.join(res_path, fg_name2))

        # 更新room_onebg_onefg.json并另存为room.json
        with open("./room_twobg_peronefg.json", "r", encoding="utf-8") as f:
            room_json = json.load(f)
      
        # 在JSON中替换素材名称
        room_json = json.dumps(room_json).replace("bg_source_name1", bg_name1).replace("bg_source_name2", bg_name2).replace("fg_source_name1", fg_name1).replace("fg_source_name2", fg_name2)

        room_json = json.loads(room_json)
      
        # 保存为room.json
        with open(os.path.join(room_path, "room.json"), "w", encoding="utf-8") as f:
            json.dump(room_json, f, indent=2)
      
        # 获取时间戳
        timestamp = get_timestamp_millis()
      
        # 从room.json获取roomId
        room_json_path = os.path.join(liveroom_path, "room", "room.json")
        with open(room_json_path, 'r') as f:
            room_data = json.load(f)
            room_id = room_data.get("roomId", "default_room_id")

        # 创建config.json
        create_config_json(liveroom_path, timestamp=timestamp,roomId=room_id,userId=USER_ID,title=OneBG_OneFG_TITLE)
      
        # 创建最终的打包zip文件，并进行嵌套加密
        # 输出文件应该放在base_dir中，而不是埋在工作目录中
        roomConfig = create_liveroom_package(liveroom_path, userId=USER_ID, title=OneBG_OneFG_TITLE, timestamp=timestamp, room_id=room_id, output_dir=base_dir, operation_id=operation_id)

        file_basename = os.path.basename(roomConfig.zip_path)
        remote_name = f"{file_basename}"
        # 执行异步上传
        upload_success = await upload_file_async(file_path=roomConfig.zip_path, remote_name=remote_name)
        if not upload_success:
            return "上传文件失败"
      
        return remote_name
  
    finally:
        # 完成后清理临时工作目录
        # 这确保我们不会留下临时文件
        if os.path.exists(work_dir):
            shutil.rmtree(work_dir)

def create_config_json(liveroom_path, timestamp, roomId, userId, title):
    """创建符合要求格式的config.json"""
  
    config = {
        "time": timestamp,
        "roomId": roomId,
        "minLevel": 2,
        "userId": userId,
        "title": title
    }
  
    with open(os.path.join(liveroom_path, "config.json"), "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2)


# 测试/调试函数也需要更新为异步版本
async def test_knowledge_base_materials(description):
    """
    异步测试函数：测试从知识库获取素材并生成直播间
    
    参数:
        description: 用户描述
    """
    print(f"📝 测试描述: {description}")
    
    # 调试信息 - 查询知识库前
    print("\n🔍 正在查询知识库...")
    
    # 查询知识库(使用异步方法)
    query_text = f"请根据以下描述提供适合的直播间素材列表：{description}"
    kb_response = await search_knowledge(query_text)
    
    # 调试信息 - 显示原始响应简化版本
    print("\n📊 知识库原始响应(简化):")
    if "data" in kb_response and "result_list" in kb_response["data"]:
        result_list = kb_response["data"]["result_list"]
        print(f"找到 {len(result_list)} 个结果")
        if len(result_list) > 0:
            sample = result_list[0]
            print("第一个结果示例:")
            if "table_chunk_fields" in sample:
                for field in sample["table_chunk_fields"]:
                    if field["field_name"] == "desc":
                        # 显示描述的前100个字符
                        desc = field["field_value"]
                        print(f"  {field['field_name']}: {desc[:100]}..." if len(desc) > 100 else desc)
                    else:
                        print(f"  {field['field_name']}: {field['field_value']}")
    
    # 解析知识库数据(使用异步方法)
    try:
        print("\n🔎 解析知识库数据...")
        materials = await get_materials(description)
        
        # 显示Material对象
        print("\n🎨 获取到的素材:")
        for i, m in enumerate(materials):
            # 截断描述以保持输出简洁
            short_desc = m.desc[:50] + "..." if len(m.desc) > 50 else m.desc
            print(f"  {i+1}. ID: {m.id}, 名称: {m.name}, 背景: {m.background}")
            print(f"     描述: {short_desc}")
            
        # 其余代码保持不变...
    except Exception as e:
        print(f"\n❌ 解析过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

def debug_mode():
    """调试模式入口函数"""
    print("="*50)
    print("🛠️  MCP 调试模式")
    print("="*50)
    
    while True:
        print("\n请选择操作:")
        print("1. 测试知识库素材查询")
        print("2. 测试直播间生成")
        print("0. 退出")
        
        choice = input("请输入选项编号: ")
        
        if choice == "1":
            description = input("\n请输入直播间描述: ")
            asyncio.run(test_knowledge_base_materials(description))
        elif choice == "2":
            # 可以添加直接测试生成直播间的功能
            print("该功能尚未实现")
        elif choice == "0":
            print("退出调试模式")
            break
        else:
            print("无效选项，请重新选择")


if __name__ == "__main__":
    # 判断是否以调试模式运行
    # debug_mode()
    # search_knowledge("冬天")

    mcp.run(transport='stdio')

    # 测试代码
    # bg = Material(id=4,name="166694771685864_encrypt.zip", background=False)
    # fg = Material(id=6,name="168422940725028_encrypt.zip", background=False)
    # result = asyncio.run(generateOneBackgroundAndOneForeground(background=bg, foreground=fg))
    # print(result)
