from __future__ import print_function
import base64
import json
from pathlib import Path
import time
import requests
from volcengine import visual
from volcengine.visual.VisualService import VisualService

import os
from alibabacloud_imageseg20191230.client import Client
from alibabacloud_imageseg20191230.models import SegmentCommonImageRequest, SegmentCommonImageAdvanceRequest
from alibabacloud_tea_openapi.models import Config
from alibabacloud_tea_util.models import RuntimeOptions

from volcenginesdkarkruntime import Ark

"""
import os
# 通过 pip install 'volcengine-python-sdk[ark]' 安装方舟SDK
from volcenginesdkarkruntime import Ark

# 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
# 初始化Ark客户端，从环境变量中读取您的API Key
client = Ark(
    # 此为默认路径，您可根据业务所在地域进行配置
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
    api_key=os.environ.get("ARK_API_KEY"),
)

imagesResponse = client.images.generate(
    model="doubao-seedream-3-0-t2i-250415",
    prompt="鱼眼镜头，一只猫咪的头部，画面呈现出猫咪的五官因为拍摄方式扭曲的效果。"
)

print(imagesResponse.data[0].url)

请求参数 
请求体
prompt string 必选
用于生成图像的提示词。
model string 必选
本次请求使用模型的 Model ID 或推理接入点 (Endpoint ID)，目前仅支持 doubao-seedream-3-0-t2i-250415。
response_format string 可选  默认值 url
指定生成图像的返回格式。支持以下两种取值：
"url"：以可下载的 JPEG 图片链接形式返回；
"b64_json"：以 Base64 编码字符串的 JSON 格式返回图像数据。
size  string 可选 默认值 1024x1024
生成图像的宽高像素，要求介于 [512 x 512, 2048 x 2048] 之间。
推荐可选的宽高：
1024x1024 （1:1）
864x1152 （3:4）
1152x864 （4:3）
1280x720 （16:9）
720x1280 （9:16）
832x1248 （2:3）
1248x832 （3:2）
1512x648 （21:9）
seed integer 可选  默认值 -1
随机数种子，用于控制模型生成内容的随机性。取值范围为 [-1, 2147483647]。如果不提供，则算法自动生成一个随机数作为种子。如果希望生成内容保持一致，可以使用相同的 seed 参数值。
guidance_scale  Float 可选 默认值 2.5
模型输出结果与prompt的一致程度，即生成图像的自由度；值越大，模型自由度越小，与用户输入的提示词相关性越强。取值范围：[1, 10] 之间的浮点数。
watermark  Boolean 可选 默认值 true
是否在生成的图片中添加水印。
false：不添加水印。
true：在图片右下角添加"AI生成"字样的水印标识。
响应参数
model string
本次请求使用的模型 ID （模型名称-版本）。
created integer
本次请求创建时间的 Unix 时间戳（秒）。
data list
输出图像的信息，包括图像下载的 URL 或 Base64。
当指定返回生成图像的格式为url时，则相应参数的子字段为url；
注意：为确保信息安全，该链接将在生成后 24 小时内失效，请务必及时保存图像。
当指定返回生成图像的格式为b64_json时，则相应参数的子字段为b64_json。
usage Object
usage.generated_images integer
模型生成的图片张数。
error  Object
error.code string 
错误码
error.message string
错误提示信息
"""
class SeedDreamAIService:
    def __init__(self):
        self.ark_client = Ark(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="1a1fc7d8-534f-4030-b8a4-65c60da4ec85",
        )
        
    def text_to_image(self, prompt: str, size: str = "1024x1024", response_format: str = "b64_json", model: str = "doubao-seedream-3-0-t2i-250415", seed: int = -1, guidance_scale: float = 2.5, watermark: bool = False) -> dict:
        """
        文本生成图片 - 同步方法
        
        Args:
            prompt: 图片描述提示词，支持中英文
            size: 图片宽高，默认1024x1024，取值范围[512x512, 2048x2048]
            response_format: 图片返回格式，默认b64_json，取值范围[url, b64_json]
            model: 模型，默认doubao-seedream-3-0-t2i-250415，取值范围[doubao-seedream-3-0-t2i-250415, doubao-seedream-3-0-t2i-250415-v2]
            seed: 随机数种子，默认-1，取值范围[-1, 2147483647]
            guidance_scale: 模型输出结果与prompt的一致程度，即生成图像的自由度；值越大，模型自由度越小，与用户输入的提示词相关性越强。取值范围：[1, 10] 之间的浮点数。
            watermark: 是否在生成的图片中添加水印。
                false：不添加水印。
                true：在图片右下角添加"AI生成"字样的水印标识。
        """
        try:
            imagesResponse = self.ark_client.images.generate(
                model=model,
                prompt=prompt,
                size=size,
                response_format=response_format,
                seed=seed,
                guidance_scale=guidance_scale,
                watermark=watermark
            )
            print(imagesResponse)
            return imagesResponse.data[0].b64_json
        except Exception as e:
            return {
                "error": f"方舟AI图片生成失败: {str(e)}",
                "code": -1
            }

class JimengAIService:
    """
    即梦AI服务封装类
    根据火山引擎官方文档实现，支持同步和异步调用
    """
    
    def __init__(self, access_key: str, secret_key: str):
        """
        初始化即梦AI服务
        
        Args:
            access_key: 火山引擎AccessKey
            secret_key: 火山引擎SecretKey
        """
        self.visual_service = VisualService()
        self.visual_service.set_ak(access_key)
        self.visual_service.set_sk(secret_key)
    
    def text_to_image(self, prompt: str, width: int = 512, height: int = 512, 
                     use_pre_llm: bool = True) -> dict:
        """
        文本生成图片 - 同步方法
        
        Args:
            prompt: 图片描述提示词，支持中英文
            width: 图片宽度，默认512，取值范围[256, 768]
            height: 图片高度，默认512，取值范围[256, 768]
            use_pre_llm: 是否开启文本扩写，默认True
            
        Returns:
            dict: 生成结果，包含图片URL或Base64数据
        """
        try:
            # 构建请求参数
            form = {
                "req_key": "jimeng_high_aes_general_v21_L",
                "prompt": prompt,
                "width": width,
                "height": height,
                "use_pre_llm": use_pre_llm,
                "use_sr": True,
                "seed": -1,
                "return_url": False
            }
            
            # 调用即梦AI接口
            resp = self.visual_service.cv_process(form)
            return resp
            
        except Exception as e:
            return {
                "error": f"即梦AI图片生成失败: {str(e)}",
                "code": -1
            }
    
    
    # 效果很差
    # huoshan: 0.03 / 张  效果很差
    # remove.bg: 1.17 / 张 效果还行 https://www.remove.bg/zh/pricing 太贵
    # pixian: > 1 / 张 根据像素计费，价格和remove.bg差不多
    # 阿里：0.007 / 张 效果跟pixian差不多
    def goodsSegment(self, image_base64: str) -> dict:
        """
        使用阿里云的分割抠图服务
        
        Args:
            image_base64: 图片base64编码字符串
            
        Returns:
            dict: 分割结果
        """
        try:
            import io
            import base64
            from alibabacloud_imageseg20191230.models import SegmentCommonImageAdvanceRequest
            
            # 配置阿里云访问凭证
            config = Config(
                access_key_id='LTAI5tQcHM7PhFXV39iybnRD',
                access_key_secret='******************************',
                endpoint='imageseg.cn-shanghai.aliyuncs.com',
                region_id='cn-shanghai'
            )
            
            # 创建客户端
            client = Client(config)
            
            # 将base64解码为二进制数据
            img_data = base64.b64decode(image_base64)
            
            # 创建内存文件对象
            img_stream = io.BytesIO(img_data)
            
            # 创建高级请求对象(用于文件流上传)
            segment_request = SegmentCommonImageAdvanceRequest()
            segment_request.image_urlobject = img_stream
            # segment_request.return_form = 'crop'
            
            # 设置运行时选项
            runtime = RuntimeOptions()
            
            # 发送请求
            response = client.segment_common_image_advance(segment_request, runtime)
            
            # 返回结果
            return {
                "code": 10000,
                "data": {
                    "img_url": response.body.data.image_url,
                    "status": "success"
                },
                "message": "Success"
            }
        except Exception as e:
            return {
                "error": f"阿里云分割抠图失败: {str(e)}",
                "code": -1
            }

def process_all_images():
    """
    遍历素材文件夹下的所有图片，进行分割抠图，
    并将结果保存到原图片所在目录，图片名后增加_aliseg
    """
    # 初始化服务
    jimeng_service = JimengAIService(
        access_key="AKLTNWIxMzU2ZjkyNWYwNGIyMGIxZWEwMGUxMDRkNzU4NDQ",
        secret_key="T1RneFpUZGxNamt3WWpVd05EZzBObUV5TnpneFpHUXlNVFF4WkRjek1EUQ=="
    )
    
    # 定义素材文件夹路径
    base_dir = Path("./素材")
    subdirs = ["贴片", "标题", "前景"]
    
    # 支持的图片扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']
    
    # 遍历所有子目录
    for subdir in subdirs:
        dir_path = base_dir / subdir
        if not dir_path.exists():
            print(f"目录不存在: {dir_path}")
            continue
            
        print(f"处理目录: {dir_path}")
        
        # 遍历目录中的所有文件
        for img_file in dir_path.glob("*"):
            # 检查是否为图片文件
            if img_file.suffix.lower() not in image_extensions:
                continue
                
            try:
                # 构建输出文件路径
                output_file = img_file.parent / f"{img_file.stem}_aliseg{img_file.suffix}"
                
                print(f"处理图片: {img_file}")
                
                # 读取图片并转换为base64
                with open(img_file, "rb") as f:
                    image_base64 = base64.b64encode(f.read()).decode('utf-8')
                
                # 调用分割抠图服务
                resp = jimeng_service.goodsSegment(image_base64=image_base64)
                
                # 检查是否成功
                if resp.get('code') == 10000 and 'img_url' in resp.get('data', {}):
                    # 下载分割后的图片
                    img_url = resp['data']['img_url']
                    img_response = requests.get(img_url)
                    
                    if img_response.status_code == 200:
                        # 保存到文件
                        with open(output_file, 'wb') as f:
                            f.write(img_response.content)
                        print(f"成功保存: {output_file}")
                    else:
                        print(f"下载图片失败: {img_url}, 状态码: {img_response.status_code}")
                else:
                    print(f"处理图片失败: {img_file}, 错误: {resp.get('error', '未知错误')}")
            
            except Exception as e:
                print(f"处理图片出错: {img_file}, 错误: {str(e)}")

# 在主函数中调用
if __name__ == "__main__":
    process_all_images()
            