import os
import json
import logging
import requests
from typing import Generator, AsyncGenerator
from typing import Dict, Any
from typing import List
import aiohttp
import asyncio

DOUBAOAPIKEY = "1a1fc7d8-534f-4030-b8a4-65c60da4ec85"

def ask_deepseekapi_r1(prompt: str, system: str) -> Generator[dict, None, None]:
    host_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"  # 关键参数
    model = "deepseek-r1-250120"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DOUBAOAPIKEY}"
    }
    data = {
        "model": model,
        "messages": [{"role": "system","content": system},{"role": "user", "content": prompt}],
        "stream": True
    }

    logging.info(f" 请求data：{data}") 

    try:
        with requests.post(
            host_url, 
            headers=headers,
            json=data,
            stream=True,
            timeout=60
        ) as response:
            response.raise_for_status()
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith("data: "):
                        try:
                            chunk = json.loads(decoded_line[6:])  # 移除 "data: " 前缀
                            # print("chunk=", chunk)
                            reasoning_content = chunk["choices"][0]["delta"].get("reasoning_content", "")
                            delta = chunk["choices"][0]["delta"].get("content", "")
                            yield {"reasoning_content": reasoning_content, "delta": delta}
                        except json.JSONDecodeError as e:
                            logging.error(f"Failed to decode JSON: {e}")
                        except Exception as e:
                            logging.error(f"Unexpected error: {e}")
                    else:
                        logging.info(f"Received non-data line: {decoded_line}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Stream API request failed: {e}")
        raise


def ask_deepseekapi_v3(prompt: str, system: str ) -> Generator[dict, None, None]:
    host_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"  # 关键参数
    model = "deepseek-v3-241226"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DOUBAOAPIKEY}"
    }
    data = {
        "model": model,
        "messages": [{"role": "system","content": system},{"role": "user", "content": prompt}],
        "stream": True
    }

    logging.info(f" 请求data：{data}") 

    try:
        with requests.post(
            host_url, 
            headers=headers,
            json=data,
            stream=True,
            timeout=60
        ) as response:
            response.raise_for_status()
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith("data: "):
                        try:
                            chunk = json.loads(decoded_line[6:])  # 移除 "data: " 前缀
                            # print("chunk=", chunk)
                            reasoning_content = chunk["choices"][0]["delta"].get("reasoning_content", "")
                            delta = chunk["choices"][0]["delta"].get("content", "")
                            yield {"reasoning_content": reasoning_content, "delta": delta}
                        except json.JSONDecodeError as e:
                            logging.error(f"Failed to decode JSON: {e}")
                        except Exception as e:
                            logging.error(f"Unexpected error: {e}")
                    else:
                        logging.info(f"Received non-data line: {decoded_line}")


    except requests.exceptions.RequestException as e:
        logging.error(f"Stream API request failed: {e}")
        raise


def ask_deepseekapir1_net(prompt: str, system: str) -> Generator[dict, None, None]:
    host_url = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions"  # 关键参数
    model = "bot-20250307180447-5dskt"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DOUBAOAPIKEY}"
    }
    data = {
        "model": model,
        "messages": [{"role": "system","content": system},{"role": "user", "content": prompt}],
        "stream": True,
        "stream_options": {"include_usage": True},
    }

    logging.info(f" 请求data：{data}") 

    try:
        with requests.post(
            host_url, 
            headers=headers,
            json=data,
            stream=True,
            timeout=60
        ) as response:
            response.raise_for_status()
            for line in response.iter_lines():
                
                if line:
                    decoded_line = line.decode('utf-8') 
                    if decoded_line.startswith("data:"):
                        try:
                            chunk = json.loads(decoded_line[5:])  # 移除 "data: " 前缀
                            # print("chunk=", chunk)
                            reasoning_content = chunk.get("choices", [{}])[0].get("delta", {}).get("reasoning_content", "")
                            delta = chunk.get("choices", [{}])[0].get("delta", {}).get("content", "")
                            yield {"reasoning_content": reasoning_content, "delta": delta}
                        except json.JSONDecodeError as e:
                            logging.error(f"Failed to decode JSON: {e}")
                        except Exception as e:
                            logging.error(f"Unexpected error: {e}")
                    else:
                        logging.info(f"Received non-data line: {decoded_line}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Stream API request failed: {e}")
        raise



def ask_deepseekapiv3_net(prompt: str, system: str) -> Generator[dict, None, None]:
    host_url = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions"  # 关键参数
    model = "bot-20250310105331-2m782"

    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DOUBAOAPIKEY}"
    }
    data = {
        "model": model,
        "messages": [{"role": "system","content": system},{"role": "user", "content": prompt}],
        "stream": True,
        "stream_options": {"include_usage": True},
    }

    logging.info(f" 请求data：{data}") 

    try:
        with requests.post(
            host_url, 
            headers=headers,
            json=data,
            stream=True,
            timeout=60
        ) as response:
            response.raise_for_status()
            for line in response.iter_lines():
                
                if line:
                    decoded_line = line.decode('utf-8') 
                    if decoded_line.startswith("data:"):
                        try:
                            chunk = json.loads(decoded_line[5:])  # 移除 "data: " 前缀
                            # print("chunk=", chunk)
                            reasoning_content = chunk.get("choices", [{}])[0].get("delta", {}).get("reasoning_content", "")
                            delta = chunk.get("choices", [{}])[0].get("delta", {}).get("content", "")
                            yield {"reasoning_content": reasoning_content, "delta": delta}
                        except json.JSONDecodeError as e:
                            logging.error(f"Failed to decode JSON: {e}")
                        except Exception as e:
                            logging.error(f"Unexpected error: {e}")
                    else:
                        logging.info(f"Received non-data line: {decoded_line}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Stream API request failed: {e}")
        raise



async def asy_ask_deepseekapi_r1(prompt: str) -> AsyncGenerator[Dict[str, Any], None]:
    host_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    model = "deepseek-r1-250120"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DOUBAOAPIKEY}"
    }
    data = {
        "model": model,
        "messages": [{"role": "user", "content": prompt}],
        "stream": True
    }

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(host_url, headers=headers, json=data) as response:
                response.raise_for_status()
                
                # 使用异步迭代器处理流式响应
                async for line in response.content:
                    decoded_line = line.decode('utf-8').strip()
                    if decoded_line.startswith("data:"):
                        chunk_str = decoded_line[5:].strip()
                        try:
                            chunk = json.loads(chunk_str)
                            yield {
                                "reasoning_content": chunk["choices"][0]["delta"].get("reasoning_content", ""),
                                "delta": chunk["choices"][0]["delta"].get("content", "")
                            }
                        except json.JSONDecodeError:
                            print(f"无效的JSON数据: {chunk_str}")
        except Exception as e:
            print(f"API请求失败: {str(e)}")
            yield {"error": str(e)}

async def main(prompt: str):
    async for response in asy_ask_deepseekapi_r1(prompt):
        print("部分响应:", response)

if __name__ == "__main__":
    asyncio.run(main("介绍一下天际这首歌"))