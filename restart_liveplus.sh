#!/bin/bash

# LivePlus服务重启脚本
# 用法: ./restart_liveplus.sh [pull]
# 参数:
#   pull - 如果指定，会先从git拉取最新代码

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 服务名称
SERVICE_NAME="liveplus"
# 项目目录
PROJECT_DIR="/root/work/mcp_liveplus"
# 日志文件
LOG_FILE="$PROJECT_DIR/restart.log"

# 记录日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 成功信息
success() {
    echo -e "${GREEN}[✓] $1${NC}" | tee -a "$LOG_FILE"
}

# 警告信息
warning() {
    echo -e "${YELLOW}[!] $1${NC}" | tee -a "$LOG_FILE"
}

# 错误信息
error() {
    echo -e "${RED}[✗] $1${NC}" | tee -a "$LOG_FILE"
}

# 检查服务状态函数
check_service() {
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        return 0
    else
        return 1
    fi
}

# 创建日志文件目录
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# 重启过程开始
log "===== 开始重启LivePlus服务 ====="

# 如果指定了pull参数，拉取最新代码
if [ "$1" == "pull" ]; then
    log "拉取Git最新代码..."
    cd "$PROJECT_DIR" || { error "无法进入项目目录: $PROJECT_DIR"; exit 1; }
    
    # 检查是否有未提交的更改
    if [[ -n $(git status -s) ]]; then
        warning "本地有未提交的更改，使用git stash保存"
        git stash save "自动保存于 $(date)"
    fi
    
    # 拉取最新代码
    if git pull; then
        success "Git代码拉取成功"
    else
        error "Git代码拉取失败"
        # 继续运行，不中断脚本
    fi
fi

# 停止服务
log "停止LivePlus服务..."
if systemctl stop "$SERVICE_NAME"; then
    success "LivePlus服务已停止"
else
    warning "无法正常停止服务，尝试强制停止进程..."
    pkill -f "gunicorn.*liveplus_client:app" || true
    sleep 2
fi

# 备份数据库（可选）
log "备份数据库..."
DB_FILE="$PROJECT_DIR/tasks.db"
if [ -f "$DB_FILE" ]; then
    BACKUP_DIR="$PROJECT_DIR/backups"
    mkdir -p "$BACKUP_DIR"
    cp "$DB_FILE" "$BACKUP_DIR/tasks_$(date +%Y%m%d_%H%M%S).db"
    success "数据库已备份到 $BACKUP_DIR"
fi

# 重新加载systemd配置（如果有更改）
log "重新加载systemd配置..."
systemctl daemon-reload

# 启动服务
log "启动LivePlus服务..."
if systemctl start "$SERVICE_NAME"; then
    success "LivePlus服务已启动"
else
    error "启动LivePlus服务失败"
    # 显示错误日志
    journalctl -u "$SERVICE_NAME" -n 20 --no-pager
    exit 1
fi

# 检查服务状态
log "验证服务状态..."
sleep 3
if check_service; then
    success "LivePlus服务运行正常"
else
    error "LivePlus服务启动失败或运行异常"
    # 显示错误日志
    journalctl -u "$SERVICE_NAME" -n 20 --no-pager
    exit 1
fi

# 检查Nginx状态（如果需要）
log "检查Nginx状态..."
if systemctl is-active --quiet nginx; then
    success "Nginx服务运行正常"
else
    warning "Nginx服务未运行，尝试启动..."
    if systemctl start nginx; then
        success "Nginx服务已启动"
    else
        error "启动Nginx服务失败"
    fi
fi

# 验证API可用性
log "验证API可用性..."
HEALTH_CHECK_URL="http://127.0.0.1:5000/healthz"
if curl -s "$HEALTH_CHECK_URL" | grep -q "ok"; then
    success "API健康检查通过"
else
    warning "API健康检查失败，服务可能未正常响应"
fi

# 显示最近的日志
log "显示最近的应用日志..."
tail -n 10 "$PROJECT_DIR/liveplus.log" || warning "无法读取应用日志"

log "===== LivePlus服务重启完成 ====="
success "服务重启过程完成！"

# 提供一些额外命令帮助
echo -e "\n${YELLOW}常用命令:${NC}"
echo -e "  ${GREEN}查看应用日志:${NC} tail -f $PROJECT_DIR/liveplus.log"
echo -e "  ${GREEN}查看服务状态:${NC} systemctl status $SERVICE_NAME"
echo -e "  ${GREEN}查看服务错误:${NC} journalctl -u $SERVICE_NAME -n 50"

