"""
MiniMax语音合成服务模块
实现同步语音合成、异步语音合成、音色管理和快速复刻功能
"""

import json
import time
import base64
import requests
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

# 配置日志
logger = logging.getLogger(__name__)

class TTSModel(Enum):
    """支持的TTS模型"""
    SPEECH_02_HD = "speech-02-hd"
    SPEECH_02_TURBO = "speech-02-turbo"
    SPEECH_01_HD = "speech-01-hd"
    SPEECH_01_TURBO = "speech-01-turbo"

class AudioFormat(Enum):
    """支持的音频格式"""
    MP3 = "mp3"
    PCM = "pcm"
    FLAC = "flac"
    WAV = "wav"

class Emotion(Enum):
    """支持的情绪"""
    HAPPY = "happy"
    SAD = "sad"
    ANGRY = "angry"
    FEARFUL = "fearful"
    DISGUSTED = "disgusted"
    SURPRISED = "surprised"
    CALM = "calm"

@dataclass
class VoiceSettings:
    """语音设置"""
    voice_id: str
    speed: float = 1.0  # 语速 [0.5, 2]
    vol: float = 1.0    # 音量 (0, 10]
    pitch: int = 0      # 语调 [-12, 12]
    emotion: Optional[str] = None  # 情绪

@dataclass
class AudioSettings:
    """音频设置"""
    sample_rate: int = 32000  # 采样率
    bitrate: int = 128000     # 比特率
    format: str = "mp3"       # 格式
    channel: int = 1          # 声道数

class MinimaxTTSService:
    """MiniMax语音合成服务"""
    
    # MiniMax系统音色列表
    SYSTEM_VOICES = {
        # 男性音色
        "male-qn-qingse": "青涩青年音色",
        "male-qn-jingying": "精英青年音色", 
        "male-qn-badao": "霸道青年音色",
        "male-qn-daxuesheng": "青年大学生音色",
        "presenter_male": "男性主持人",
        "audiobook_male_1": "男性有声书1",
        "audiobook_male_2": "男性有声书2",
        
        # 女性音色
        "female-shaonv": "少女音色",
        "female-yujie": "御姐音色",
        "female-chengshu": "成熟女性音色",
        "female-tianmei": "甜美女性音色",
        "presenter_female": "女性主持人",
        "audiobook_female_1": "女性有声书1",
        "audiobook_female_2": "女性有声书2",
        
        # Beta版音色
        "male-qn-qingse-jingpin": "青涩青年音色-beta",
        "male-qn-jingying-jingpin": "精英青年音色-beta",
        "male-qn-badao-jingpin": "霸道青年音色-beta",
        "male-qn-daxuesheng-jingpin": "青年大学生音色-beta",
        "female-shaonv-jingpin": "少女音色-beta",
        "female-yujie-jingpin": "御姐音色-beta",
        "female-chengshu-jingpin": "成熟女性音色-beta",
        "female-tianmei-jingpin": "甜美女性音色-beta",
        
        # 儿童音色
        "clever_boy": "聪明男童",
        "cute_boy": "可爱男童",
        "lovely_girl": "萌萌女童",
        "cartoon_pig": "卡通猪小琪",
        
        # 特色音色
        "bingjiao_didi": "病娇弟弟",
        "junlang_nanyou": "俊朗男友",
        "chunzhen_xuedi": "纯真学弟",
        "lengdan_xiongzhang": "冷淡学长",
        "badao_shaoye": "霸道少爷",
        "tianxin_xiaoling": "甜心小玲",
        "qiaopi_mengmei": "俏皮萌妹",
        "wumei_yujie": "妩媚御姐",
        "diadia_xuemei": "嗲嗲学妹",
        "danya_xuejie": "淡雅学姐",
        
        # 英文音色
        "Santa_Claus": "Santa Claus",
        "Grinch": "Grinch",
        "Rudolph": "Rudolph",
        "Arnold": "Arnold",
        "Charming_Santa": "Charming Santa",
        "Charming_Lady": "Charming Lady",
        "Sweet_Girl": "Sweet Girl",
        "Cute_Elf": "Cute Elf",
        "Attractive_Girl": "Attractive Girl",
        "Serene_Woman": "Serene Woman",
    }
    
    def __init__(self, api_key: str, group_id: str):
        """
        初始化MiniMax TTS服务
        
        Args:
            api_key: MiniMax API密钥
            group_id: 用户组ID
        """
        self.api_key = api_key
        self.group_id = group_id
        self.base_url = "https://api.minimaxi.com/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def get_system_voices(self) -> Dict[str, str]:
        """
        获取所有系统音色
        
        Returns:
            Dict[str, str]: 音色ID到音色名称的映射
        """
        return self.SYSTEM_VOICES.copy()
    
    def sync_text_to_speech(
        self,
        text: str,
        model: str = TTSModel.SPEECH_02_HD.value,
        voice_settings: Optional[VoiceSettings] = None,
        audio_settings: Optional[AudioSettings] = None,
        stream: bool = False,
        language_boost: Optional[str] = None,
        output_format: str = "hex"
    ) -> Dict[str, Any]:
        """
        同步语音合成
        
        Args:
            text: 待合成的文本（最大10000字符）
            model: 使用的模型
            voice_settings: 语音设置
            audio_settings: 音频设置
            stream: 是否流式输出
            language_boost: 语言增强
            output_format: 输出格式 ("hex" 或 "url")
            
        Returns:
            Dict[str, Any]: 合成结果
        """
        if not text or len(text) > 10000:
            raise ValueError("文本长度必须在1-10000字符之间")
        
        # 默认设置
        if voice_settings is None:
            voice_settings = VoiceSettings(voice_id="male-qn-qingse")
        if audio_settings is None:
            audio_settings = AudioSettings()
        
        # 构建请求体
        payload = {
            "model": model,
            "text": text,
            "stream": stream,
            "output_format": output_format,
            "voice_setting": {
                "voice_id": voice_settings.voice_id,
                "speed": voice_settings.speed,
                "vol": voice_settings.vol,
                "pitch": voice_settings.pitch
            },
            "audio_setting": {
                "sample_rate": audio_settings.sample_rate,
                "bitrate": audio_settings.bitrate,
                "format": audio_settings.format,
                "channel": audio_settings.channel
            }
        }
        
        # 添加可选参数
        if voice_settings.emotion:
            payload["voice_setting"]["emotion"] = voice_settings.emotion
        
        if language_boost:
            payload["language_boost"] = language_boost
        
        try:
            url = f"{self.base_url}/t2a_v2?GroupId={self.group_id}"
            
            if stream:
                # 流式请求
                response = requests.post(
                    url, 
                    headers=self.headers, 
                    json=payload, 
                    stream=True,
                    timeout=120
                )
                return self._handle_stream_response(response)
            else:
                # 非流式请求
                response = requests.post(
                    url, 
                    headers=self.headers, 
                    json=payload,
                    timeout=120
                )
                response.raise_for_status()
                return response.json()
                
        except requests.exceptions.RequestException as e:
            logger.error(f"同步语音合成请求失败: {str(e)}")
            return {"error": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"同步语音合成出错: {str(e)}")
            return {"error": f"处理失败: {str(e)}"}
    
    def _handle_stream_response(self, response) -> Dict[str, Any]:
        """处理流式响应"""
        try:
            audio_chunks = []
            for chunk in response.iter_content(chunk_size=1024):
                if chunk and chunk.startswith(b'data:'):
                    try:
                        data = json.loads(chunk[5:])
                        if "data" in data and "audio" in data["data"]:
                            audio_chunks.append(data["data"]["audio"])
                    except json.JSONDecodeError:
                        continue
            
            return {
                "success": True,
                "audio_chunks": audio_chunks,
                "total_chunks": len(audio_chunks)
            }
        except Exception as e:
            return {"error": f"流式响应处理失败: {str(e)}"}

    def async_text_to_speech(
        self,
        text: Optional[str] = None,
        text_file_id: Optional[int] = None,
        model: str = TTSModel.SPEECH_02_HD.value,
        voice_settings: Optional[VoiceSettings] = None,
        audio_settings: Optional[AudioSettings] = None,
        language_boost: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        异步长文本语音合成

        Args:
            text: 待合成的文本（最大50000字符）
            text_file_id: 文本文件ID（与text二选一）
            model: 使用的模型
            voice_settings: 语音设置
            audio_settings: 音频设置
            language_boost: 语言增强

        Returns:
            Dict[str, Any]: 任务创建结果，包含task_id
        """
        if not text and not text_file_id:
            raise ValueError("text和text_file_id必须提供其中一个")

        if text and len(text) > 50000:
            raise ValueError("文本长度不能超过50000字符")

        # 默认设置
        if voice_settings is None:
            voice_settings = VoiceSettings(voice_id="male-qn-qingse")
        if audio_settings is None:
            audio_settings = AudioSettings(channel=2)  # 异步默认双声道

        # 构建请求体
        payload = {
            "model": model,
            "voice_setting": {
                "voice_id": voice_settings.voice_id,
                "speed": voice_settings.speed,
                "vol": voice_settings.vol,
                "pitch": voice_settings.pitch
            },
            "audio_setting": {
                "sample_rate": audio_settings.sample_rate,
                "bitrate": audio_settings.bitrate,
                "format": audio_settings.format,
                "channel": audio_settings.channel
            }
        }

        # 添加文本或文件ID
        if text:
            payload["text"] = text
        else:
            payload["text_file_id"] = text_file_id

        # 添加可选参数
        if voice_settings.emotion:
            payload["voice_setting"]["emotion"] = voice_settings.emotion

        if language_boost:
            payload["language_boost"] = language_boost

        try:
            url = f"{self.base_url}/t2a_async_v2?GroupId={self.group_id}"
            response = requests.post(
                url,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"异步语音合成请求失败: {str(e)}")
            return {"error": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"异步语音合成出错: {str(e)}")
            return {"error": f"处理失败: {str(e)}"}

    def query_async_task(self, task_id: str) -> Dict[str, Any]:
        """
        查询异步语音合成任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """
        try:
            url = f"http://api.minimaxi.com/v1/query/t2a_async_query_v2?GroupId={self.group_id}&task_id={task_id}"
            response = requests.get(
                url,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"查询异步任务失败: {str(e)}")
            return {"error": f"查询失败: {str(e)}"}
        except Exception as e:
            logger.error(f"查询异步任务出错: {str(e)}")
            return {"error": f"查询失败: {str(e)}"}

    def upload_file(self, file_path: str, purpose: str = "voice_clone") -> Dict[str, Any]:
        """
        上传文件到MiniMax

        Args:
            file_path: 文件路径
            purpose: 文件用途 ("voice_clone" 或 "prompt_audio")

        Returns:
            Dict[str, Any]: 上传结果，包含file_id
        """
        try:
            url = f"{self.base_url}/files/upload?GroupId={self.group_id}"

            # 准备文件上传
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {'purpose': purpose}
                headers = {'Authorization': f'Bearer {self.api_key}'}

                response = requests.post(
                    url,
                    headers=headers,
                    data=data,
                    files=files,
                    timeout=120
                )
                response.raise_for_status()
                return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"文件上传失败: {str(e)}")
            return {"error": f"上传失败: {str(e)}"}
        except Exception as e:
            logger.error(f"文件上传出错: {str(e)}")
            return {"error": f"上传失败: {str(e)}"}

    def voice_clone(
        self,
        file_id: int,
        voice_id: str,
        clone_prompt: Optional[Dict[str, Any]] = None,
        text_validation: Optional[str] = None,
        demo_text: Optional[str] = None,
        demo_model: Optional[str] = None,
        accuracy: float = 0.7,
        need_noise_reduction: bool = False,
        need_volume_normalization: bool = False
    ) -> Dict[str, Any]:
        """
        快速复刻音色

        Args:
            file_id: 音频文件ID
            voice_id: 自定义音色ID（8-256字符，首字符必须是字母）
            clone_prompt: 音色复刻参数
            text_validation: 文本验证（最大200字符）
            demo_text: 试听文本（最大2000字符）
            demo_model: 试听模型
            accuracy: 文本校验准确率阈值 [0,1]
            need_noise_reduction: 是否开启降噪
            need_volume_normalization: 是否开启音量归一化

        Returns:
            Dict[str, Any]: 复刻结果
        """
        # 验证voice_id格式
        if not self._validate_voice_id(voice_id):
            return {"error": "voice_id格式不正确：长度8-256字符，首字符必须是字母，只允许字母、数字、-、_，末位不能是-或_"}

        # 构建请求体
        payload = {
            "file_id": file_id,
            "voice_id": voice_id,
            "accuracy": accuracy,
            "need_noise_reduction": need_noise_reduction,
            "need_volume_normalization": need_volume_normalization
        }

        # 添加可选参数
        if clone_prompt:
            payload["clone_prompt"] = clone_prompt

        if text_validation:
            if len(text_validation) > 200:
                return {"error": "text_validation长度不能超过200字符"}
            payload["text_validation"] = text_validation

        if demo_text:
            if len(demo_text) > 2000:
                return {"error": "demo_text长度不能超过2000字符"}
            payload["text"] = demo_text

            if demo_model:
                payload["model"] = demo_model
            else:
                return {"error": "提供demo_text时必须指定demo_model"}

        try:
            url = f"{self.base_url}/voice_clone?GroupId={self.group_id}"
            response = requests.post(
                url,
                headers=self.headers,
                json=payload,
                timeout=120
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"音色复刻失败: {str(e)}")
            return {"error": f"复刻失败: {str(e)}"}
        except Exception as e:
            logger.error(f"音色复刻出错: {str(e)}")
            return {"error": f"复刻失败: {str(e)}"}

    def _validate_voice_id(self, voice_id: str) -> bool:
        """验证voice_id格式"""
        if not voice_id or len(voice_id) < 8 or len(voice_id) > 256:
            return False

        # 首字符必须是字母
        if not voice_id[0].isalpha():
            return False

        # 末位字符不能是-或_
        if voice_id[-1] in ['-', '_']:
            return False

        # 只允许字母、数字、-、_
        allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_')
        if not all(c in allowed_chars for c in voice_id):
            return False

        return True
