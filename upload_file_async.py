from http.client import HTTPException
import os
import tos
import aiohttp
import asyncio
from concurrent.futures import ThreadPoolExecutor
import logging

# 创建logger
logger = logging.getLogger(__name__)

# TOS配置
access_key = "AKLTNWIxMzU2ZjkyNWYwNGIyMGIxZWEwMGUxMDRkNzU4NDQ"
access_secret = "T1RneFpUZGxNamt3WWpVd05EZzBObUV5TnpneFpHUXlNVFF4WkRjek1EUQ=="
endpoint = "tos-cn-beijing.volces.com"
region = "cn-beijing"
bucket_name = "image-generation"

# 创建线程池用于执行同步TOS操作
executor = ThreadPoolExecutor()

async def upload_file_async(file_path: str, remote_name: str) -> bool:
    """异步上传文件到TOS存储"""
    try:
        loop = asyncio.get_event_loop()
        
        # 在线程池中执行同步TOS操作
        return await loop.run_in_executor(
            executor,
            _upload_to_tos,
            file_path,
            remote_name
        )
    except Exception as e:
        logger.error(f"上传失败: {str(e)}")
        return False

async def download_file_async(file_name: str, save_path: str) -> str:
    """异步从TOS下载文件"""
    try:
        # 确保目标目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        loop = asyncio.get_event_loop()
        
        # 在线程池中执行同步TOS操作
        result = await loop.run_in_executor(
            executor,
            _download_from_tos,
            file_name,
            save_path
        )
        
        if not result:
            raise HTTPException(status_code=404, detail="文件下载失败")
        
        return save_path
    except tos.exceptions.TosServerError as e:
        logger.error(f"TOS服务端错误: {e.message}, RequestID: {e.request_id}")
        raise HTTPException(status_code=e.status_code, detail=f"存储服务错误: {e.message}")
    except tos.exceptions.TosClientError as e:
        logger.error(f"TOS客户端错误: {e.message}")
        raise HTTPException(status_code=400, detail=f"请求错误: {e.message}")
    except Exception as e:
        logger.error(f"下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文件下载服务不可用")

# 同步TOS上传操作，将在线程池中执行
def _upload_to_tos(file_path, remote_name):
    try:
        client = tos.TosClientV2(access_key, access_secret, endpoint, region)
        client.put_object_from_file(bucket_name, remote_name, file_path)
        return True
    except tos.exceptions.TosClientError as e:
        logger.error(f'上传客户端错误: {e.message}, 原因: {e.cause}')
        return False
    except tos.exceptions.TosServerError as e:
        logger.error(f'上传服务端错误: {e.code}, RequestID: {e.request_id}, 消息: {e.message}')
        return False
    except Exception as e:
        logger.error(f'上传未知错误: {e}')
        return False

# 同步TOS下载操作，将在线程池中执行
def _download_from_tos(file_name, local_path):
    try:
        client = tos.TosClientV2(access_key, access_secret, endpoint, region)
        client.get_object_to_file(bucket_name, file_name, local_path)
        return local_path
    except tos.exceptions.TosClientError as e:
        logger.error(f'下载客户端错误: {e.message}, 原因: {e.cause}')
        return None
    except tos.exceptions.TosServerError as e:
        logger.error(f'下载服务端错误: {e.code}, RequestID: {e.request_id}, 消息: {e.message}')
        return None
    except Exception as e:
        logger.error(f'下载未知错误: {e}')
        return None