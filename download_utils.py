import os
import zipfile
import hashlib
import httpx
import shutil
import asyncio
from pathlib import Path
from download_material_fast import download_file_async
import logging

logger = logging.getLogger("liveplus")

def get_material_source_download_folder():
    """Get the download folder path, similar to iOS implementation"""
    # Using current directory + sourceDownload as default location
    base_path = os.path.join(os.getcwd(), "sourceDownload")
    return base_path

async def download_zip_source(file_name: str, source: str, output_file_name: str):
    """
    从TOS下载加密的zip文件并解密
    
    参数:
        file_name: TOS中的文件名
        source: 源文件名（预期包含_encrypt表示加密）
        output_file_name: 最终保存的文件名
        
    返回:
        tuple: (最终路径, 最终文件名) 或失败时返回 (None, None)
    """
    folder_path = get_material_source_download_folder()
    zip_path = os.path.join(folder_path, source)
    final_path = os.path.join(folder_path, output_file_name)
    
    logger.info(f"下载文件 {file_name}")

    # 创建文件夹（如果不存在）
    os.makedirs(folder_path, exist_ok=True)
    
    # 检查文件是否已存在
    if os.path.exists(final_path):
        print(f"文件已存在: {final_path}")
        return final_path, output_file_name
    
    # 检查是否为加密文件
    if "_encrypt" not in source:
        print("非加密文件，跳过处理")
        return None, None
    
    # 生成zip密码的MD5哈希
    zip_key = hashlib.md5(source.encode()).hexdigest()
    
    try:
        # 从TOS下载文件
        await download_file_async(file_name, zip_path)
        
        # 解压缩带密码的zip文件
        extracted_files = []
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # 获取zip内的文件列表
            file_list = zip_ref.namelist()
            # 解压所有文件
            zip_ref.extractall(path=folder_path, pwd=bytes(zip_key, 'utf-8'))
            # 记录解压的文件
            extracted_files = [os.path.join(folder_path, f) for f in file_list]
        
        # 解压后删除zip文件
        if os.path.exists(zip_path):
            os.remove(zip_path)
        
        # 检查是否有解压出的文件
        if not extracted_files:
            return None, None
            
        # 为简单起见，我们使用第一个解压出的文件
        # 在实际实现中，可能需要更多逻辑来确定正确的文件
        extracted_path = extracted_files[0]
        extracted_name = os.path.basename(extracted_path)
        
        # 处理MP4文件的大小写转换
        file_ext = os.path.splitext(extracted_name)[1]
        if file_ext.upper() == '.MP4':
            file_base = os.path.splitext(extracted_name)[0]
            new_file_name = f"{file_base}{file_ext.lower()}"
            new_path = os.path.join(folder_path, new_file_name)
            
            # 重命名文件
            os.rename(extracted_path, new_path)
            return new_path, new_file_name
        
        return extracted_path, extracted_name
        
    except Exception as e:
        print(f"下载或解压文件时出错: {e}")
        return None, None

