#!/usr/bin/env python3
"""
MiniMax TTS功能演示脚本
展示如何使用MiniMax语音合成服务的各项功能
"""

import os
import json
from minimax_tts_service import MinimaxTTSService, VoiceSettings, AudioSettings

def demo_system_voices():
    """演示获取系统音色功能"""
    print("🎵 演示：获取系统音色")
    print("-" * 40)
    
    # 注意：这里需要实际的API密钥和组ID
    api_key = "your_api_key_here"
    group_id = "your_group_id_here"
    
    if api_key == "your_api_key_here":
        print("⚠️  请先配置API密钥和组ID")
        print("   在代码中替换 api_key 和 group_id 变量")
        return
    
    # 创建服务实例
    tts_service = MinimaxTTSService(api_key=api_key, group_id=group_id)
    
    # 获取系统音色
    voices = tts_service.get_system_voices()
    
    print(f"✅ 获取到 {len(voices)} 个系统音色")
    
    # 按类别显示音色
    categories = {
        'male': '男性音色',
        'female': '女性音色', 
        'children': '儿童音色',
        'special': '特色音色',
        'english': '英文音色'
    }
    
    for category, category_name in categories.items():
        category_voices = {}
        for voice_id, voice_name in voices.items():
            if category == 'male' and (voice_id.startswith('male-') or voice_id in ['presenter_male', 'audiobook_male_1', 'audiobook_male_2']):
                category_voices[voice_id] = voice_name
            elif category == 'female' and (voice_id.startswith('female-') or voice_id in ['presenter_female', 'audiobook_female_1', 'audiobook_female_2']):
                category_voices[voice_id] = voice_name
            elif category == 'children' and voice_id in ['clever_boy', 'cute_boy', 'lovely_girl', 'cartoon_pig']:
                category_voices[voice_id] = voice_name
            elif category == 'special' and voice_id in ['bingjiao_didi', 'junlang_nanyou', 'chunzhen_xuedi', 'lengdan_xiongzhang', 'badao_shaoye', 'tianxin_xiaoling', 'qiaopi_mengmei', 'wumei_yujie', 'diadia_xuemei', 'danya_xuejie']:
                category_voices[voice_id] = voice_name
            elif category == 'english' and '_' in voice_id and not voice_id.startswith(('male-', 'female-')):
                category_voices[voice_id] = voice_name
        
        if category_voices:
            print(f"\n📂 {category_name} ({len(category_voices)}个):")
            for voice_id, voice_name in list(category_voices.items())[:5]:  # 只显示前5个
                print(f"   • {voice_id}: {voice_name}")
            if len(category_voices) > 5:
                print(f"   ... 还有 {len(category_voices) - 5} 个音色")

def demo_sync_tts():
    """演示同步语音合成功能"""
    print("\n🎤 演示：同步语音合成")
    print("-" * 40)
    
    # 注意：这里需要实际的API密钥和组ID
    api_key = "your_api_key_here"
    group_id = "your_group_id_here"
    
    if api_key == "your_api_key_here":
        print("⚠️  请先配置API密钥和组ID")
        return
    
    # 创建服务实例
    tts_service = MinimaxTTSService(api_key=api_key, group_id=group_id)
    
    # 配置语音参数
    voice_settings = VoiceSettings(
        voice_id="female-tianmei",  # 甜美女性音色
        speed=1.0,                  # 正常语速
        vol=1.0,                    # 正常音量
        pitch=0,                    # 原始音调
        emotion="happy"             # 高兴情绪
    )
    
    audio_settings = AudioSettings(
        sample_rate=32000,          # 32kHz采样率
        bitrate=128000,             # 128kbps比特率
        format="mp3",               # MP3格式
        channel=1                   # 单声道
    )
    
    # 测试文本
    test_text = "你好！这是MiniMax语音合成的演示。我是甜美女性音色，正在用高兴的情绪为您朗读这段文字。"
    
    print(f"📝 合成文本: {test_text}")
    print(f"🎭 使用音色: {voice_settings.voice_id}")
    print(f"😊 情绪设置: {voice_settings.emotion}")
    
    # 执行同步语音合成
    result = tts_service.sync_text_to_speech(
        text=test_text,
        model="speech-02-hd",
        voice_settings=voice_settings,
        audio_settings=audio_settings,
        output_format="hex"
    )
    
    if 'error' in result:
        print(f"❌ 合成失败: {result['error']}")
    else:
        print("✅ 合成成功!")
        if 'data' in result and 'extra_info' in result:
            extra_info = result['extra_info']
            print(f"   • 音频时长: {extra_info.get('audio_length', 0)}ms")
            print(f"   • 音频大小: {extra_info.get('audio_size', 0)} bytes")
            print(f"   • 计费字符: {extra_info.get('usage_characters', 0)} 字符")

def demo_async_tts():
    """演示异步语音合成功能"""
    print("\n⏳ 演示：异步语音合成")
    print("-" * 40)
    
    # 注意：这里需要实际的API密钥和组ID
    api_key = "your_api_key_here"
    group_id = "your_group_id_here"
    
    if api_key == "your_api_key_here":
        print("⚠️  请先配置API密钥和组ID")
        return
    
    # 创建服务实例
    tts_service = MinimaxTTSService(api_key=api_key, group_id=group_id)
    
    # 配置语音参数
    voice_settings = VoiceSettings(
        voice_id="male-qn-qingse",  # 青涩青年音色
        speed=1.2,                  # 稍快语速
        vol=1.0,                    # 正常音量
        pitch=0,                    # 原始音调
        emotion="calm"              # 平静情绪
    )
    
    audio_settings = AudioSettings(
        sample_rate=32000,          # 32kHz采样率
        bitrate=128000,             # 128kbps比特率
        format="mp3",               # MP3格式
        channel=2                   # 双声道（异步默认）
    )
    
    # 长文本示例
    long_text = """
    这是一个异步长文本语音合成的演示。异步语音合成适用于处理较长的文本内容，
    比如整篇文章、书籍章节或者长篇报告。与同步合成不同，异步合成会返回一个任务ID，
    您可以通过这个任务ID来查询合成进度和获取最终结果。
    
    MiniMax的异步语音合成支持最大50000字符的文本输入，能够处理各种复杂的文本内容，
    包括标点符号、数字、英文单词等。同时还支持自定义语音停顿时间，
    让合成的语音更加自然流畅。
    
    这项技术在有声书制作、语音播报、教育培训等场景中有着广泛的应用前景。
    """ * 3  # 重复3次以增加文本长度
    
    print(f"📝 文本长度: {len(long_text)} 字符")
    print(f"🎭 使用音色: {voice_settings.voice_id}")
    print(f"😌 情绪设置: {voice_settings.emotion}")
    
    # 提交异步任务
    result = tts_service.async_text_to_speech(
        text=long_text,
        model="speech-02-hd",
        voice_settings=voice_settings,
        audio_settings=audio_settings
    )
    
    if 'error' in result:
        print(f"❌ 任务提交失败: {result['error']}")
    else:
        task_id = result.get('task_id')
        file_id = result.get('file_id')
        print("✅ 异步任务提交成功!")
        print(f"   • 任务ID: {task_id}")
        print(f"   • 文件ID: {file_id}")
        print("   • 可使用任务ID查询合成进度")

def demo_voice_clone():
    """演示音色复刻功能"""
    print("\n🎭 演示：音色复刻")
    print("-" * 40)
    
    print("⚠️  音色复刻需要提供音频文件")
    print("📋 音频文件要求:")
    print("   • 格式: MP3, M4A, WAV")
    print("   • 时长: 10秒 - 5分钟")
    print("   • 大小: 最大20MB")
    print("   • 内容: 清晰的人声录音")
    
    print("\n🔧 voice_id命名规则:")
    print("   • 长度: 8-256字符")
    print("   • 首字符: 必须是字母")
    print("   • 允许字符: 字母、数字、-、_")
    print("   • 末位字符: 不能是-或_")
    print("   • 示例: MyVoice001, CustomVoice_2024")
    
    print("\n💡 使用步骤:")
    print("   1. 准备符合要求的音频文件")
    print("   2. 调用 upload_file() 上传音频")
    print("   3. 调用 voice_clone() 执行复刻")
    print("   4. 获得自定义音色ID，可用于语音合成")

def main():
    """主演示函数"""
    print("🚀 MiniMax TTS功能演示")
    print("=" * 50)
    
    print("📌 使用前请先配置API密钥:")
    print("   1. 访问 https://platform.minimaxi.com")
    print("   2. 获取API密钥和组ID")
    print("   3. 在演示代码中替换相应变量")
    print("   4. 或设置环境变量 MINIMAX_API_KEY 和 MINIMAX_GROUP_ID")
    
    # 检查环境变量
    api_key = os.getenv("MINIMAX_API_KEY")
    group_id = os.getenv("MINIMAX_GROUP_ID")
    
    if api_key and group_id:
        print(f"✅ 检测到环境变量配置")
        print(f"   API密钥: {api_key[:10]}...")
        print(f"   组ID: {group_id}")
    else:
        print("⚠️  未检测到环境变量配置")
    
    # 运行各项演示
    demo_system_voices()
    demo_sync_tts()
    demo_async_tts()
    demo_voice_clone()
    
    print("\n" + "=" * 50)
    print("✅ 演示完成")
    print("📖 更多详细信息请参考 MINIMAX_TTS_README.md")

if __name__ == "__main__":
    main()
